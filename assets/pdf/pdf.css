@font-face {
  font-family: "Museo Sans 300";
  src:
    url("./../../assets/fonts/museo-sans-300.woff2") format("woff2"),
    url("./../../assets/fonts/museo-sans-300.woff") format("woff"),
    url("./../../assets/fonts/museo-sans-300.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
  font-stretch: normal;
}

@font-face {
  font-family: "Museo Sans 500";
  src:
    url("./../../assets/fonts/museo-sans-500.woff2") format("woff2"),
    url("./../../assets/fonts/museo-sans-500.woff") format("woff"),
    url("./../../assets/fonts/museo-sans-500.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
  font-stretch: normal;
}

@font-face {
  font-family: "Museo Sans 700";
  src:
    url("./../../assets/fonts/museo-sans-700.woff2") format("woff2"),
    url("./../../assets/fonts/museo-sans-700.woff") format("woff"),
    url("./../../assets/fonts/museo-sans-700.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
  font-stretch: normal;
}

@font-face {
  font-family: "Museo Sans 900";
  src:
    url("./../../assets/fonts/museo-sans-900.woff2") format("woff2"),
    url("./../../assets/fonts/museo-sans-900.woff") format("woff"),
    url("./../../assets/fonts/museo-sans-900.otf") format("opentype");
  font-weight: 900;
  font-style: normal;
  font-stretch: normal;
}

thead tr {
  height: 30pt;
  width: 100%;
  background-color: #00a19c;
}

thead tr td {
  border-top-style: solid;
  border-top-width: 1pt;
  border-top-color: #4d4d4f;
}

tbody tr td {
  padding: 20px 0px;
}

tbody tr td:first-child {
  padding-left: 0px;
}

u, a {
  color: #00a99d;
}

/* tr td {
  border-top-style: solid;
  border-top-width: 1pt;
  border-top-color: #4d4d4f;
  border-bottom-style: solid;
  border-bottom-width: 1pt;
  border-bottom-color: #4d4d4f;
  color: black;
} */

ol li p {
  padding-left: 22pt;
  text-indent: -14pt;
  text-align: left;
}

ol li ol {
  padding-left: 22pt;
}

* {
  margin: 0;
  padding: 0;
  text-indent: 0;
}

table {
  width: 100%;
  margin: 0 auto !important;
}

.s2 {
  font-family: "Museo Sans 300";
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  font-size: 8pt;
  vertical-align: 1pt;
}

.s3 {
  font-family: "Times New Roman", serif;
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  font-size: 8pt;
}

h2 {
  color: #fff;
  font-family: "Museo Sans 900";
  font-style: normal;
  font-weight: bold;
  text-decoration: none;
  font-size: 9pt;
}

p {
  font-family: "Museo Sans 900";
  font-style: normal;
  font-weight: bold;
  text-decoration: none;
  font-size: 11pt;
  margin: 0pt;
}

h1 {
  font-family: "Museo Sans 700";
  font-style: normal;
  font-weight: bold;
  text-decoration: none;
  font-size: 15pt;
}

.s4 {
  /* title */
  color: #fff;
  font-family: "Museo Sans 700";
  font-style: normal;
  font-weight: bold;
  text-decoration: none;
  font-size: 13pt;
  text-align: center;
}

.s5 {
  font-family: "Museo Sans 300";
  font-style: normal;
  font-weight: 500;
  text-decoration: none;
  font-size: 13pt;
}

.s6 {
  font-family: "Museo Sans 300";
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  font-size: 10pt;
}

h3 {
  color: #00a99d;
  font-family: "Museo Sans 700";
  font-style: normal;
  font-weight: bold;
  text-decoration: none;
  font-size: 8pt;
}

.s7 {
  font-family: "Museo Sans 300";
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  font-size: 8pt;
}

.s8 {
  font-family: "Times New Roman", serif;
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  font-size: 10pt;
}

ol {
  list-style-type: none;
}

ol.decimal {
  counter-reset: c1 1;
}

ol.decimal li > *:first-child:before {
  counter-increment: c1;
  content: counter(c1, decimal) ". ";
  color: black;
  font-family: "Museo Sans 300";
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  font-size: 13pt;
}

ol.decimal > li:first-child > *:first-child:before {
  counter-increment: c1 0;
}

ol.upper-roman {
  counter-reset: c2 1;
}

ol.upper-roman > li > *:first-child:before {
  counter-increment: c2;
  content: counter(c2, upper-roman) ". ";
  color: black;
  font-family: "Museo Sans 300";
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  font-size: 13pt;
}

ol.upper-roman > li:first-child > *:first-child:before {
  counter-increment: c2 0;
}

table {
  border-collapse: collapse;
}

table,
tbody {
  vertical-align: top;
  overflow: visible;
}


/* custom */
/* GLOBAL SCROLLBAR – applies to entire page */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  /* box-shadow: inset 0 0 5px grey;  */
  border-radius: 10px;
  background:#ffffff; 
}
 
/* Handle */
::-webkit-scrollbar-thumb {
  background: #763F98; 
  border-radius: 10px;
}
/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #b30000; 
}
.content{
    width: 98%;
    background: #fff;
    border-radius: 20px;
  
}
.content .title{
  padding: 10px 20px;
}
.content tbody tr td{
    padding: 20px 20px !important;
 
}
.content thead tr {
    height: 30pt;
    width: 100%;
    background-color: #ffffff00;
}
.content thead tr p {
  color: #000 !important;
}
.content th, .content td{
  border: 0.1px solid;
}
.content table {
  border-radius: 20px;
  overflow: hidden;
  border-collapse: separate;
  border-spacing: 0;
  border: 0.1px solid;
}

/* Remove border-radius from all inner elements */
.content thead,
.content tbody,
.content tr,
.content th,
.content td {
  border-radius: 0 !important;
}

/* Fix for multiple thead/tbody sections */
.content thead:first-of-type tr:first-child th:first-child,
.content thead:first-of-type tr:first-child td:first-child,
.content tbody:first-of-type tr:first-child td:first-child {
  border-top-left-radius: 0 !important;
}

.content thead:first-of-type tr:first-child th:last-child,
.content thead:first-of-type tr:first-child td:last-child,
.content tbody:first-of-type tr:first-child td:last-child {
  border-top-right-radius: 0 !important;
}

.content thead:last-of-type tr:last-child th:first-child,
.content tbody:last-of-type tr:last-child td:first-child {
  border-bottom-left-radius: 0 !important;
}

.content thead:last-of-type tr:last-child th:last-child,
.content tbody:last-of-type tr:last-child td:last-child {
  border-bottom-right-radius: 0 !important;
}

@media screen and (max-width: 450px) {
  .content{
    width: 98%;
    /* background: #fff; */
    border-radius: 0px;
    border: 0px solid;
    border-radius: 0px;
  }
  .content table{
    border-radius: 8px;
  }
  body{
    background: #fff;
  }
  ::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }
  .content tbody tr td p{
      font-size: 9pt !important;
  }
  .content tbody tr td{
      padding: 10px 15px !important;
  }

}
