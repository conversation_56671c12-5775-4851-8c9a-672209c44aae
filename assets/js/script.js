$(document).ready(function () {
    var hash = window.location.hash;
    if (hash) {
        $('html, body').animate({
            scrollTop: $(hash).offset().top
        }, 500);
    }

    $('a[href*="#"]').on('click', function (event) {
        var target = $(this).attr('href');
        event.preventDefault();

        $('html, body').animate({
            scrollTop: $(target).offset().top
        }, 500, function () {
            window.location.hash = target;
        });
    });

    if (window.matchMedia('(min-width: 640px)').matches) {
        $('.js-tl-item').hover(function () {
            $(this).siblings().find('h4').css('display', 'none');
            $(this).find('h4').css('display', 'block');
        }, function () {
            $(this).siblings().find('h4').css('display', 'block');
        });
    }
});

$(".js-header-link").on("click", function (e) {
    if (window.matchMedia("(min-width: 992px)").matches) {
        // do nothing
    } else {
        event.preventDefault(); // Prevent default link behavior
        $(".hamburger").click();
        // Scroll to the section after a short delay to allow sidebar to close
        setTimeout(() => {
            const headerHeight = $('#navbar').outerHeight();
            const targetId = $(this).attr('href').substring(1); // Get target section ID
            const targetElement = $('#' + targetId);
            if (targetElement.length) {
                $('html, body').animate({
                    scrollTop: targetElement.offset().top - (headerHeight / 2)
                }, 500); // Adjust scroll animation speed as needed
            }
        }, 300); // Adjust delay time as needed
    }
})


$(document).ready(function () {
      gsap.registerPlugin(ScrollTrigger);

  ScrollTrigger.config({
    ignoreMobileResize: false,
  });

  // Refreshing setup
  window.addEventListener("load", () => {
    setTimeout(() => ScrollTrigger.refresh(), 300);
  });

  window.addEventListener("resize", () => {
    setTimeout(() => ScrollTrigger.refresh(), 200);
  });

  window.addEventListener("orientationchange", () => {
    setTimeout(() => ScrollTrigger.refresh(), 300);
  });

  window.addEventListener("touchstart", () => {
    ScrollTrigger.refresh();
  }, { passive: true });

  window.addEventListener("touchend", () => {
    setTimeout(() => ScrollTrigger.refresh(), 100);
  }, { passive: true });

});


$(document).on("start:animation", function () {
    $(".sticky-nav-bar").addClass("complete");

    
    // pir 2024
    $(document).on("click", ".read-more-cta[href*='.pdf']", function(e) {
        // Stop propagation to prevent other handlers from catching this
        e.stopPropagation();           
        // Get the href
        const pdfUrl = $(this).attr('href');
        // Open the PDF directly
        window.location.href = pdfUrl;
        return true;
    });

    // navbar
    let lastScrollTop = 0;
    $(window).on("scroll", function () {
    let currentScroll = $(this).scrollTop();

    if (currentScroll <= 0) {
        // At top of page
        $("#navbar").removeClass("hide show");
    } else if (currentScroll > lastScrollTop) {
        // Scrolling down
        $("#navbar").removeClass("show").addClass("hide");
    } else {
        // Scrolling up
        $("#navbar").removeClass("hide").addClass("show");
    }
    lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
    });

    //text split
    let lineSplit, wordSplit, charSplit;

    // === SPLIT SETUP ===
    function runSplitTypes() {
    if (lineSplit) lineSplit.revert();
    if (wordSplit) wordSplit.revert();
    if (charSplit) charSplit.revert();

    // Split by lines
    lineSplit = new SplitType(".split-lines", { types: "lines" });
    $(".split-lines .line").addClass("line-row");

    // Split by words
    wordSplit = new SplitType(".split-words", { types: "words" });

    // Split by chars (also gets lines & words)
    charSplit = new SplitType(".split-chars", { types: "chars" });
    }
    // === ON LOAD ===
    runSplitTypes();

    // === ON RESIZE ===
    let windowWidth = window.innerWidth;
    window.addEventListener("resize", () => {
    if (window.innerWidth !== windowWidth) {
        windowWidth = window.innerWidth;
        runSplitTypes();
    }
    });

    function triggerLineAnimationsOnScroll() {
        $(".line-animation-columns-container").each(function () {
            var $container = $(this);

            // Skip if already animated
            if ($container.hasClass("animated")) return;

            // Optional readiness check
            var isReady = $container.attr("data-trigger-ready") === "true" || !$container.is("[data-after-cards]");

            // Viewport visibility check
            var windowHeight = $(window).height();
            var elementTop = $container.offset().top;
            var elementBottom = elementTop + $container.outerHeight();
            var scrollTop = $(window).scrollTop();
            var scrollBottom = scrollTop + windowHeight;

            var isVisible = elementTop < scrollBottom - windowHeight * 0.4 && elementBottom > scrollTop;

            // Trigger animation
            if (isVisible && isReady) {
                setTimeout(function () {
                    $container.find(".line-animation-panel-content").addClass("slide-out");
                    $container.addClass("animated");
                    animateTextFill($container); // make sure animateTextFill is globally defined
                }, 750);
            }
        });
    }

    // Call once on page load
    triggerLineAnimationsOnScroll();

    // Also call on scroll
    $(window).on("scroll", function() {
        triggerLineAnimationsOnScroll();
    });


    window.addEventListener("touchmove", triggerLineAnimationsOnScroll, { passive: true });
    // Optional: trigger on resize
    $(window).on("resize", triggerLineAnimationsOnScroll);

    // === Master Animation Function (title + row) ===
    function animateTextFill(scope) {
        const isHighlightSection = $(scope).hasClass("highlight-section");
        const tl = gsap.timeline({ delay: 2 }); // delay after panel animation

        // Step 1: Animate "Energising a" (black fill)
        const blackChars = $(scope).find(".black .char");
        if (blackChars.length) {
        blackChars.each(function (i, char) {
            tl.to(char, {
            color: "#00000099",
            duration: 0.10,
            ease: "linear"
            }, `+=0`);
        });
        }

        // Step 2: Animate "Sustainable Future" (green fill)
        const greenChars = $(scope).find(".green .char");
        if (greenChars.length) {

            // if ($(scope).hasClass("main-banner")) {
            //     // Step 2a: (Optional) set immediate base color to black if needed
            //     tl.set(greenChars, { color: "#000000", duration: 0.02 });
            // }

            // Step 2b: Animate each char to green
            greenChars.each(function (i, char) {
                tl.to(char, {
                color: "#00A99D",
                duration: 0.10,
                ease: "linear"
                }, `+=0`);
            });
        }

        if (isHighlightSection) {
            // Fade in the text container after title animation
            const textContainer = $(scope).find(".line-animation-text");
            
            // Check if this text container has already been animated
            if (!textContainer.hasClass('animated-text')) {
                textContainer.addClass('animated-text');
                tl.fromTo(textContainer, 
                    { opacity: 0, y: 20 },
                    { opacity: 1, y: 0, duration: 0.5, ease: "power2.out" },
                    "+=0.3" // slight delay after title animation
                );
            }
        }

        // Step 3: Word-by-word subtitle animation
        $(scope).find(".word").each(function (i, el) {
            tl.to(el, {
                color: "#00000099",
                duration: 0.3,
                ease: "linear"
            }, `+=0.02`);
        });

        // Step 4: Paragraph row-by-row animation (if exists)
        $(scope).find(".line-row").each(function (i, el) {
            tl.to(el, {
                color: "#00A99D",
                duration: 0.5,
                ease: "linear"
            }, `+=0.005`);
        });

    }


    function otheranimations() {
        // overview 
        $(".scrol-text-animation1 .section-content .text-animation-rows").each(function (_, element) {
            const tl = gsap.timeline({
                scrollTrigger: {
                    trigger: element,
                    start: "top center+=300",
                    end: "bottom center",
                    toggleActions: "play none none none",
                    scrub: true,
                    // delay: 0.5
                    //  markers: true 
                }
            });

           const rows = $(element).find(".line-row");
            if (rows.length) {
                rows.each(function (i, row) {
                    tl.to(row, {
                        color: "#00A99D",
                        duration: 0.5,
                        ease: "linear"
                    }, `+=0.01`);
                });
            }
        });

        // financial statement section
        $(".financial-statement-section").each(function (_, element) {
            const tl = gsap.timeline({
                scrollTrigger: {
                    trigger: element,
                    start: "top 30%",
                    end: "bottom center",
                    toggleActions: "play none none none",
                }
            });

            // 1. First animate the background image
            tl.to(".financial-statement-background", {
                opacity: 1,
                scale: 1.01,
                duration: 1,
                ease: "power2.inOut"
            });

            // 1. First animate the text lines
            $(element).find(".line-row").each(function (i, row) {
                tl.to(row, {
                    y: 0,
                    opacity: 1,
                    color: "#00A99D",
                    duration: 1,
                    ease: "power3.out"
                }, i * 0.3); // Staggered start
            }, ">");
            
            // 2. Then animate the right section (book image)
            tl.to(".financial-statement-right", {
                opacity: 1,
                transform: "translateX(0%)",
                duration: 1,
                ease: "power2.out"
            }, "-=0.5"); // Start 0.5 seconds before the text animation completes
            
            // 3. Finally animate the CTA button
            tl.to(".financial-statement-section .read-more-cta", {
                opacity: 1,
                transform: "translateX(0)",
                duration: 0.8,
                ease: "power2.out"
            }, ">");

        });

        // financial tabs section
        $("#financial").each(function (_, element) {
            const tl = gsap.timeline({
                scrollTrigger: {
                    trigger: element,
                    start: "top 30%",
                    end: "bottom center",
                    toggleActions: "play none none none",
                }
            });
            
            tl.to(".full-panel h2", {
                opacity: 1,
                transform: "translateX(0%)",
                duration: 1,
                ease: "power2.out"
            }); 
            
            tl.to(".left-panel h2", {
                opacity: 1,
                transform: "translateX(0%)",
                duration: 1,
                ease: "power2.out"
            }); 

            tl.to(".financial-intro", {
                opacity: 1,
                transform: "translateX(0)",
                duration: 1,
                ease: "power2.out"
            }, "-=0.8");

            tl.to(".tab-buttons", {
                opacity: 1,
                transform: "translateY(0)",
                duration: 1,
                ease: "power2.out"
            }, "-=0.8");

            tl.to(".financial-bars", {
                opacity: 1,
                transform: "translateX(0)",
                duration: 1,
                ease: "power2.out"
            }, "-=0.8");

            tl.to(".right-panel p#description", {
                opacity: 1,
                transform: "translateX(0)",
                duration: 1,
                ease: "power2.out"
            }, "-=0.8");

        });

        //  // Direct initialization for financial animations
        // $(document).ready(function() {
        //     // Simple function to animate count-up elements
        //     function animateCountUp() {
        //         $('.count-up').each(function() {
        //             const $this = $(this);
        //             const targetValue = parseFloat($this.data('value') || 0);
                    
        //             // Reset to zero
        //             $this.text('0.0');
                    
        //             // Animation variables
        //             let currentValue = 0;
        //             const duration = 1500;
        //             const interval = 16;
        //             const increment = targetValue / (duration / interval);
                    
        //             // Clear any existing animation
        //             if ($this.data('countTimer')) {
        //                 clearInterval($this.data('countTimer'));
        //             }
                    
        //             // Start new animation
        //             const timer = setInterval(() => {
        //                 currentValue += increment;
                        
        //                 if (currentValue >= targetValue) {
        //                     $this.text(targetValue.toFixed(1));
        //                     clearInterval(timer);
        //                 } else {
        //                     $this.text(currentValue.toFixed(1));
        //                 }
        //             }, interval);
                    
        //             // Store timer reference
        //             $this.data('countTimer', timer);
        //         });
        //     }
            
        //     // Function to trigger bar animations
        //     function triggerBarAnimations() {
        //         // First reset any existing animations
        //         $(".financial-bar-2023, .financial-bar-2024").removeClass("animate");
                
        //         // Get the active tab data
        //         const activeKey = $(".tabs-item.active").data("key");
        //         const data = financialData[activeKey];
                
        //         if (!data) return;
                
        //         // Reset widths to 0 and temporarily hide bars
        //         $(".financial-bar-2023, .financial-bar-2024").css({
        //             "width": "0%",
        //             "opacity": "0"
        //         });
                
        //         // Force reflow to ensure animation works
        //         void $(".financial-bar-2023")[0]?.offsetWidth;
                
        //         // Calculate responsive widths
        //         const calculateResponsiveWidth = (baseWidth) => {
        //             const screenWidth = window.innerWidth;
        //             let width = parseFloat(baseWidth);
        //             if (screenWidth < 480) return `${width * 0.9}%`;
        //             else if (screenWidth < 768) return `${width * 0.95}%`;
        //             else return `${width}%`;
        //         };
                
        //         // Get width values from data
        //         const width2023 = data.width2023 || "100";
        //         const width2024 = data.width2024 || "100";
                
        //         // Apply responsive widths
        //         const responsiveWidth2023 = calculateResponsiveWidth(width2023);
        //         const responsiveWidth2024 = calculateResponsiveWidth(width2024);
                
        //         // After a short delay, show the bars and start animation
        //         setTimeout(() => {
        //             // Make bars visible again
        //             $(".financial-bar-2023, .financial-bar-2024").css("opacity", "1");
                    
        //             // Start animations with staggered timing
        //             $(".financial-bar-2023").css("width", responsiveWidth2023).addClass("animate");
        //             setTimeout(() => {
        //                 $(".financial-bar-2024").css("width", responsiveWidth2024).addClass("animate");
        //             }, 250);
                    
        //             // Also trigger SVG animations if needed
        //             ['barAnimation2023', 'barAnimation2024'].forEach((id, index) => {
        //                 const barAnim = document.getElementById(id);
        //                 if (barAnim) {
        //                     const newBar = barAnim.cloneNode(true);
        //                     barAnim.parentNode.replaceChild(newBar, barAnim);
                            
        //                     if (index === 1) {
        //                         setTimeout(() => newBar.beginElement(), 100);
        //                     } else {
        //                         newBar.beginElement();
        //                     }
        //                 }
        //             });
        //         }, 50);
        //     }
            
        //     // Initialize animations
        //     triggerBarAnimations();
        //     animateCountUp();
            
        //     // Also trigger on scroll into view
        //     const observer = new IntersectionObserver((entries) => {
        //         entries.forEach(entry => {
        //             if (entry.isIntersecting) {
        //                 triggerBarAnimations();
        //                 animateCountUp();
        //                 observer.unobserve(entry.target);
        //             }
        //         });
        //     }, { threshold: 0.2 });
            
        //     const financialSection = document.getElementById('financial');
        //     if (financialSection) {
        //         observer.observe(financialSection);
        //     }
            
        //     // Handle tab clicks
        //     $(".tabs-item").on("click", function() {
        //         $(".tabs-item").removeClass("active");
        //         $(this).addClass("active");
                
        //         // Get the active tab data
        //         const activeKey = $(this).data("key");
        //         const data = financialData[activeKey];
                
        //         if (data) {
        //             // Immediately hide the bars before starting new animation
        //             $(".financial-bar-2023, .financial-bar-2024").css({
        //                 "width": "0%",
        //                 "opacity": "0"
        //             });
                    
        //             // Update description and labels
        //             $("#description").text(data.text);
        //             $("#label2023").html(`RM <span class="count-up" data-value="${data.label2023.match(/\d+\.?\d*/)[0]}">0</span> <br>billion`);
        //             $("#label2024").html(`RM <span class="count-up" data-value="${data.label2024.match(/\d+\.?\d*/)[0]}">0</span> <br>billion`);
                    
        //             // Force reflow to ensure animation works
        //             void $(".financial-bar-2023")[0]?.offsetWidth;
                    
        //             // After a short delay, show the bars and start animation
        //             setTimeout(() => {
        //                 // Make bars visible again
        //                 $(".financial-bar-2023, .financial-bar-2024").css("opacity", "1");
                        
        //                 // Trigger animations
        //                 triggerBarAnimations();
        //                 animateCountUp();
        //             }, 50);
        //         }
        //     });
            
        //     // Set initial scales on page load
        //     const activeKey = $(".tabs-item.active").data("key");
        //     const data = financialData[activeKey];
            
        //     if (data) {
        //         // Set initial scale for 2023
        //         $(".financial-bar-row:first-child .financial-bar svg g").css({
        //             'transform': 'scaleX(1)',
        //             'transform-origin': 'left center'
        //         });
                
        //         // Set initial scale for 2024
        //         $(".financial-bar-row:nth-child(2) .financial-bar svg g").css({
        //             'transform': `scaleX(${data.scaleX2024 || 1})`,
        //             'transform-origin': 'left center'
        //         });
                
        //         // Ensure clip rectangles are reset
        //         document.querySelectorAll('.financial-bar-svg').forEach(svg => {
        //             svg.style.opacity = '1';
        //         });
                
        //         // Trigger initial animations
        //         setTimeout(() => {
        //             triggerBarAnimations();
        //         }, 100);
        //     }
        // });

        // // key messages section - transition from center effect
        // $(".key-message-content").each(function (_, element) {
        //     const tl = gsap.timeline({
        //         scrollTrigger: {
        //             trigger: element,
        //             start: "top 30%",
        //             end: "bottom center",
        //             toggleActions: "play none none none",
        //         }
        //     });
            
        //     // Get columns based on their semantic classes
        //     const imageCol = $(element).find(".image-column");
        //     const msgCol = $(element).find(".message-column");
            
        //     const isImageRight = $(element).hasClass("image-right");
            
        //     // Set initial states based on layout class
        //     if (isImageRight) {
        //         // Image is on right, message on left
        //         gsap.set(imageCol, { 
        //             xPercent: -50,
        //             opacity: 0,
        //             zIndex: 2
        //         });
                
        //         gsap.set(msgCol, { 
        //             xPercent: 50,
        //             opacity: 0,
        //             zIndex: 1
        //         });
        //     } else {
        //         // Image is on left, message on right (default)
        //         gsap.set(imageCol, { 
        //             xPercent: 50,
        //             opacity: 0,
        //             zIndex: 2
        //         });
                
        //         gsap.set(msgCol, { 
        //             xPercent: -50,
        //             opacity: 0,
        //             zIndex: 1
        //         });
        //     }
            
        //     // Animation sequence remains the same
        //     // Animation sequence with mobile check
        //     if (window.innerWidth > 768) {
        //         tl.to(imageCol, {
        //             opacity: 1,
        //             duration: 0.8,
        //             ease: "power2.out"
        //         })
        //         .to(imageCol, {
        //             xPercent: 0,
        //             duration: 1,
        //             ease: "power3.inOut"
        //         }, "+=0.3")
        //     } else {
        //         tl.to(imageCol, {
        //             opacity: 1,
        //             xPercent: 0,
        //             duration: 1,
        //             ease: "power3.inOut"
        //         });
        //     }
        //     tl.to(msgCol, {
        //         xPercent: 0,
        //         opacity: 1,
        //         duration: 1,
        //         ease: "power3.inOut"
        //     }, "-=0.8")
        //     .to($(element).find(".quote-content, .quote-message"), {
        //         opacity: 1,
        //         y: 0,
        //         duration: 1,
        //         // stagger: 0.15,
        //         ease: "power3.out"
        //     }, "-=0.3")
        //     .to($(element).find(".quote-wrapper .bgelements"), {
        //         opacity: 1,
        //         duration: 2,
        //         transform: "translateX(0)",
        //         ease: "power3.out" // Fixed capitalization of "out"
        //     }, "-=0.8")
        //     .to($(element).find(".read-more-cta"), { // More specific to current element
        //         opacity: 1,
        //         duration: 0.8,
        //         transform: "translateX(0)",
        //         ease: "power2.out" // Fixed capitalization of "out"
        //     }, "-=1.5");
        // });

        //  // Energy transition strategy
        // $(".energy-transition-strategy").each(function (_, element) {
        //     const tl = gsap.timeline({
        //         scrollTrigger: {
        //             trigger: element,
        //             start: "top 30%",
        //             end: "bottom center",
        //             toggleActions: "play none none none",
        //         }
        //     });
            
        //     tl.to(".energy-transition-strategy h2", {
        //         opacity: 1,
        //         duration: 1,
        //         ease: "power2.out"
        //     }); 

        //     tl.to(".ets-card", {
        //         opacity: 1,
        //         transform: "translateY(0)",
        //         duration: 1,
        //         ease: "power2.out"
        //     }, "-=0.8");

        //     tl.to(".ets-card-content", {
        //         opacity: 1,
        //         transform: "translateY(0)",
        //         duration: 1,
        //         ease: "power2.out"
        //     }, "-=0.8");

        // });

        // // non-financial content index
        // $("#non-financial-content-index").each(function (_, element) {
        //     const tl = gsap.timeline({
        //         scrollTrigger: {
        //             trigger: element,
        //             start: "top 30%",
        //             end: "bottom center",
        //             toggleActions: "play none none none",
        //         }
        //     });
        //     tl.to(".accordion", {
        //         opacity: 1,
        //         transform: "translateY(0)",
        //         duration: 1,
        //         ease: "power2.out"
        //     });
        // });

        // // === Scroll-triggered rows animation (text-animation-rows) ===
        // $(".scrol-text-animation2 .section-content .text-animation-rows").each(function (_, element) {
        //     const tl = gsap.timeline({
        //         scrollTrigger: {
        //             trigger: element,
        //             start: "top center",
        //             end: "bottom center",
        //             toggleActions: "play none none none",
        //             scrub: true,
        //         }
        //     });

        //     $(element).find(".line-row").each(function (i, row) {
        //         tl.to(row, {
        //             color: "#00A99D",
        //             duration: 0.5,
        //             ease: "linear"
        //         }, `+=0.01`);
        //     });
        // });
    

    }
    otheranimations();
    window.addEventListener("resize", () => {
        otheranimations();
    });
    $(window).on("resize", otheranimations);

    // // $(document).ready(function() {otheranimations();});
    // // $(window).on("resize", otheranimations);
    // // window.addEventListener("touchstart", () => ScrollTrigger.refresh(), { passive: true });
    // // window.addEventListener("touchend", () => setTimeout(() => ScrollTrigger.refresh(), 100), { passive: true });
    // // Also call on scroll
    // $(window).on("scroll", function() {
    //     otheranimations();
    // });

    

    // downloads
    // $("#downloads").each(function (_, element) {
    //     const tl = gsap.timeline({
    //         scrollTrigger: {
    //             trigger: element,
    //             start: "top 30%",
    //             end: "bottom center",
    //             toggleActions: "play none none none",
    //         }
    //     });
    //     tl.to(".left-title-container", {
    //         opacity: 1,
    //         // transform: "translateX(0)",
    //         duration: 1,
    //         ease: "power2.out"
    //     });
    //     tl.to(".carousel-wrapper", {
    //         opacity: 1,
    //         // transform: "translateX(0)",
    //         duration: 1,
    //         ease: "power2.out"
    //     });
    // });

    // cards stack
    const cards = gsap.utils.toArray(".card");
    const section = document.querySelector("#energy-strategy-cards");

    let tlcards = gsap.timeline({
        scrollTrigger: {
        trigger: section,
        start: "top top",
        end: () => "+=" + cards.length * 585,
        scrub: true,
        pin: section,
        anticipatePin: 1,
        onLeave: () => {
                ScrollTrigger.refresh(true); // refresh after unpinning to fix downstream triggers
            }
        }
    });

    cards.forEach((card, index) => {
        const scale = 1 - index * 0.20;
        const offsetY = index * - 150;

        // Animate card in
        const initialY = index === 0 ? "10%" : "200%";
        tlcards.fromTo(card, 
            { opacity: 1, y: initialY }, 
            { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" }
        );

        // Animate scale & position of current and previous cards
        cards.slice(0, index).forEach((prevCard, j) => {
            tlcards.to(prevCard, {
                scale: 1 - (index - j) * 0.20,
                y: -(index - j) * 150,
                opacity: 1,
                duration: 0.3,
                ease: "power2.out"
            }, "<"); // sync
            // Animate ::after via CSS variable
            tlcards.to(prevCard, {
                css: {
                "--after-opacity": 1,
                },
                duration: 0.3
            }, "<");

            // Animate .card-content margin-top via CSS variable
            const content = prevCard.querySelector(".card-content");
            if (content) {
                tlcards.to(content, {
                y:-30,
                duration: 0.3
                }, "<");
            }
        });
    });

    // sustainability circle diagram
    function initSustainabilityCircles() {
        // Set initial states based on screen size
        function setInitialStates() {
            // Default values for large screens (>1280px)
            let centerScale = 1.25;
            let centerTop = "16%";
            let leftRightTop = "5%";
            
            // Final positions
            let finalCenterTop = "62%";
            let finalLeftRightTop = "69.5%";
            let finalLeftPosition = "3.5%";
            let finalRightPosition = "3.5%"; // For right circle, we'll use calc(100% - this)
            
            // Adjust values based on screen width
            if (window.innerWidth <= 1280 && window.innerWidth > 767) {
                // For screens 1025px-1280px
                centerScale = 1.3;
                finalLeftPosition = "5%";
                finalRightPosition = "5%";
                finalLeftRightTop = "68%";
                finalCenterTop = "75%";
            }
            else if (window.innerWidth <= 767) {
                // For mobile screens
                centerScale = 1.35;
                finalLeftPosition = "5%";
                finalRightPosition = "5%";
                finalLeftRightTop = "55%";
                finalCenterTop = "63%";
            }
            
            // Set initial states for all screen sizes
            gsap.set(".center-circle", { 
                scale: centerScale, 
                opacity: 1,
                top: centerTop,  
                left: "50%",  
                right: "auto",
                transform: "translate(-50%, 0%)",
                transformOrigin: "center bottom" 
            });
            
            gsap.set(".top-circle", { opacity: 1 }); // Already in correct position
            
            gsap.set(".left-circle", { 
                opacity: 1,
                top: leftRightTop,   
                left: "50%",  
                right: "auto",
                transform: "translate(-50%, 0%)",
                transformOrigin: "left bottom" 
            });
            
            gsap.set(".right-circle", {
                opacity: 1,
                top: leftRightTop,
                left: "auto",
                right: "50%",
                transform: "translate(50%, 0%)",
                transformOrigin: "right bottom" 
            });
            
            // Create timeline for the animation
            const tlCircles = gsap.timeline({
                scrollTrigger: {
                    trigger: "#creating-sustainable-value",
                    start: "top 20%",
                    end: "bottom bottom",
                    toggleActions: "play none none none"
                }
            });
            
            // Animation sequence
            tlCircles
                // First animate center circle
                .to(".center-circle", { 
                    scale: 1, 
                    top: finalCenterTop,
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    duration: 1.2, 
                    ease: "power2.inOut" 
                })
                
                // After center circle animation completes, move left circle
                .to(".left-circle", {  
                    top: finalLeftRightTop, 
                    left: window.innerWidth <= 767 ? "3%" : finalLeftPosition, 
                    right: "auto",
                    transform: "translateY(-50%)",
                    duration: 1, 
                    ease: "power2.inOut" 
                }, "+=0.1")
                
                // For right circle, animate from right-center to right-edge
                .to(".right-circle", { 
                    top: finalLeftRightTop,
                    right: window.innerWidth <= 767 ? "3%" : finalRightPosition,
                    transform: "translateY(-50%)", 
                    duration: 1, 
                    ease: "power2.inOut" 
                }, "-=0.8");
        }
        
        // Run initial setup
        setInitialStates();
        
        // Update on window resize
        let resizeTimeout;
        window.addEventListener("resize", function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                // Clear any existing animations
                const triggers = ScrollTrigger.getAll();
                triggers.forEach(trigger => trigger.kill());
                
                // Re-initialize with new values
                setInitialStates();
            }, 250); // Debounce resize events
        });
    }
    // Call the function to initialize the animation
    initSustainabilityCircles();
    
    // Financial section data and functionality
    const financialData = {
        revenue: {
            label2023: "RM 343.6 billion",
            label2024: "RM 320.0 billion",
            scaleX2023: 0.82,
            scaleX2024: 0.70,    // 320.0/343.6 ≈ 0.92
            text: "Revenue declined by seven per cent from the previous year, primarily due to lower average realised LNG prices and divestment of the Engen Group, partially offset by higher sales volume."
        },
        pat: {
            label2023: "RM 80.7 billion",
            label2024: "RM 55.1 billion",
            scaleX2023: 0.45,
            scaleX2024: 0.3,    // 55.1/84.7 ≈ 0.65
            text: "PAT reduced to RM55.1 billion in 2024, impacted by lower prices, absence of favourable tax adjustments from 2023, and foreign currency translation reserves from the Engen Group divestment."
        },
        totalassets: {
            label2023: "RM 773.3 billion",
            label2024: "RM 766.7 billion",
            scaleX2023: 1,
            scaleX2024: 0.95,    // 766.7/773.3 ≈ 0.99
            text: "We maintained a strong asset base in 2024 with RM766.7 billion in total assets, reflecting continued financial resilience despite a marginal dip from 2023."
        },
        cffo: {
            label2023: "RM 114.2 billion",
            label2024: "RM 102.5 billion",
            scaleX2023: 0.6,
            scaleX2024: 0.55,    // 114.2/102.5 ≈ 1.11
            text: "In 2024, we maintained a robust CFFO of RM102.5 billion, enabling us to sustain operations, explore growth opportunities, and fulfil our dividend commitments."
        }
    };

    // Financial section JavaScript
    const financialSection = {
        animateNumbers: function() {
            // Clear any existing animations first
            document.querySelectorAll('.count-up').forEach(element => {
                if (element._countTimer) {
                    clearInterval(element._countTimer);
                    element._countTimer = null;
                }
            });

            const activeKey = $(".tabs-item.active").data("key");
            const data = financialData[activeKey];
            
            if (!data) return;

            document.querySelectorAll('.count-up').forEach(element => {
                const parent = element.closest('.financial-bar-label');
                const parentId = parent.id;
                const valueStr = parentId === 'label2023' ? data.label2023 : data.label2024;
                const match = valueStr.match(/\d+\.?\d*/);
                const targetValue = match ? parseFloat(match[0]) : 0;

                element.textContent = "0.0";
                let currentValue = 0;
                const duration = 1500;
                const interval = 16;
                const increment = targetValue / (duration / interval);

                element._countTimer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= targetValue) {
                        element.textContent = targetValue.toFixed(1);
                        clearInterval(element._countTimer);
                        element._countTimer = null;
                    } else {
                        element.textContent = currentValue.toFixed(1);
                    }
                }, interval);
            });
        },

        updateBarScales: function() {
            const activeKey = $(".tabs-item.active").data("key");
            const data = financialData[activeKey];
            
            if (!data) return;
            
            // Reset SVG clip paths immediately
            document.querySelectorAll('#clipRect2023, #clipRect2024').forEach(rect => {
                if (rect) rect.setAttribute('width', '0');
            });
            
            // Set the scale for both bars
            const scaleX2023 = data.scaleX2023 || 1;
            const scaleX2024 = data.scaleX2024 || 1;
            
            // Apply scales to SVGs - ensure this happens before animation
            const svg2024 = document.querySelector('.financial-bar-row:nth-child(1) .financial-bar svg g');
            const svg2023 = document.querySelector('.financial-bar-row:nth-child(2) .financial-bar svg g');
            
            if (svg2023) {
                svg2023.style.transform = `scaleX(${scaleX2023})`;
                svg2023.style.transformOrigin = 'left center';
            }
            
            if (svg2024) {
                svg2024.style.transform = `scaleX(${scaleX2024})`;
                svg2024.style.transformOrigin = 'left center';
            }
            
            // Ensure clip rectangles are reset
            document.querySelectorAll('.financial-bar-svg').forEach(svg => {
                svg.style.opacity = '1';
            });
            
            // Trigger clip mask animations after a brief delay
            setTimeout(() => {
                this.animateBarClipMasks();
            }, 50);
        },
        
        animateBarClipMasks: function() {
            // First reset any existing animations
            ['barAnimation2023', 'barAnimation2024'].forEach((id) => {
                const barAnim = document.getElementById(id);
                if (barAnim) {
                    // Reset the animation by cloning and replacing
                    const newBar = barAnim.cloneNode(true);
                    barAnim.parentNode.replaceChild(newBar, barAnim);
                }
            });
            
            // Make sure SVGs are visible before animation
            document.querySelectorAll('.financial-bar svg').forEach(svg => {
                svg.style.opacity = '1';
            });
            
            // Trigger the animations with a slight delay
            setTimeout(() => {
                const barAnim2023 = document.getElementById('barAnimation2023');
                if (barAnim2023) barAnim2023.beginElement();
                
                // Add a slight delay for the second bar
                setTimeout(() => {
                    const barAnim2024 = document.getElementById('barAnimation2024');
                    if (barAnim2024) barAnim2024.beginElement();
                }, 100);
            }, 50);
        },

        resetLabels: function() {
            $("#label2023, #label2024").each(function() {
                $(this).html(`RM <span class="count-up">0</span> <br>billion`);
            });
        },

        updateContent: function(key) {
            const data = financialData[key];
            if (!data) return;

            // Update description text
            $("#description").text(data.text);
            
            // Update labels
            $("#label2023").html(`RM <span class="count-up" data-value="${data.label2023.match(/\d+\.?\d*/)[0]}">0</span> <br>billion`);
            $("#label2024").html(`RM <span class="count-up" data-value="${data.label2024.match(/\d+\.?\d*/)[0]}">0</span> <br>billion`);
        },

        init: function() {
            // Set default active tab if none is selected
            if (!$(".tabs-item.active").length) {
                $(".tabs-item").first().addClass("active");
            }

            const activeKey = $(".tabs-item.active").data("key");
            const data = financialData[activeKey];
            
            // Immediately reset clip paths to ensure bars start hidden
            document.querySelectorAll('#clipRect2023, #clipRect2024').forEach(rect => {
                if (rect) rect.setAttribute('width', '0');
            });
            
            // Set initial scales for both bars
            if (data) {
                const scaleX2023 = data.scaleX2023 || 1;
                const scaleX2024 = data.scaleX2024 || 1;
                
                const svg2024 = document.querySelector('.financial-bar-row:nth-child(1) .financial-bar svg g');
                const svg2023 = document.querySelector('.financial-bar-row:nth-child(2) .financial-bar svg g');
                
                if (svg2023) svg2023.style.transform = `scaleX(${scaleX2023})`;
                if (svg2024) svg2024.style.transform = `scaleX(${scaleX2024})`;
            }
            
            // Update content and animations
            this.updateContent(activeKey);
            this.updateBarScales();
            this.animateNumbers();
        },

        updateTab: function(key) {
            const data = financialData[key];
            if (!data) return;
            
            // Immediately hide SVG content by resetting clip paths
            document.querySelectorAll('#clipRect2023, #clipRect2024').forEach(rect => {
                if (rect) rect.setAttribute('width', '0');
            });
            
            // Reset scales for both bars
            const svg2024 = document.querySelector('.financial-bar-row:nth-child(1) .financial-bar svg g');
            const svg2023 = document.querySelector('.financial-bar-row:nth-child(2) .financial-bar svg g');
        
            
            // Update content
            this.updateContent(key);
            
            // Reset labels
            this.resetLabels();
            
            // Short delay before starting animations
            setTimeout(() => {
                // Start animations
                this.animateBarClipMasks();
                
                // Animate numbers
                this.animateNumbers();

                if (svg2023) svg2023.style.transform = `scaleX(${data.scaleX2023 || 1})`;
                if (svg2024) svg2024.style.transform = `scaleX(${data.scaleX2024 || 1})`;
            }, 50);
        }
    };

    $(document).ready(function() {
        // First tab setup
        if (!$(".tabs-item.active").length) {
            $(".tabs-item").first().addClass("active");
        }

        // Immediately reset clip paths to ensure bars start hidden
        document.querySelectorAll('#clipRect2023, #clipRect2024').forEach(rect => {
            if (rect) rect.setAttribute('width', '0');
        });

        // Scroll trigger to initialize when section is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    financialSection.init();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.2 });

        const financialSectionElement = document.getElementById('financial');
        if (financialSectionElement) observer.observe(financialSectionElement);

        // Tab click event
        $(".tabs-item").on("click", function() {
            $(".tabs-item").removeClass("active");
            $(this).addClass("active");
            const key = $(this).data("key");
            financialSection.updateTab(key);
        });
    });


// // === Global Resize Handler for All GSAP Animations ===
// let resizeDebounce;
// window.addEventListener("resize", () => {
//     clearTimeout(resizeDebounce);
//     resizeDebounce = setTimeout(() => {
//         // Kill all existing ScrollTriggers
//         ScrollTrigger.getAll().forEach(trigger => trigger.kill());

//         // Re-run any layout-sensitive logic
//         runSplitTypes(); // Re-split text lines/chars/words
//         initSustainabilityCircles(); // Reposition and animate circles
//         otheranimations();

//         // Optional: rebuild card stack animation
//         // rebuildCardTimeline(); // <- define this if your cards layout depends on screen size

//         // Refresh ScrollTrigger after rebuilding
//         ScrollTrigger.refresh();
//     }, 300);
// });

    
});


// downloads section
$(document).ready(function() {
    const $carousel = $('#carousel-download');
    const $activeCard = $('#active-card');
    const $carouselTitle = $('#carousel-title');
    const $downloadButton = $('.download-button');
    const $wrapper = $('.carousel-wrapper');
    const isMobile = $(window).width() <= 768;
    
    // Clone items for infinite loop with fade transitions
    function cloneItemsWithFade() {
        // Store original items
        const $originalItems = $carousel.find('.download-item');
        const itemCount = $originalItems.length;
        
        // Fade out carousel
        $carousel.css('opacity', '0');
        
        // Wait for fade out
        setTimeout(() => {
            // Clear carousel and rebuild with original items first
            $carousel.empty();
            $originalItems.each(function(index) {
                const $newItem = $(this).clone();
                $newItem.attr('data-original-index', index);
                $carousel.append($newItem);
            });
            
            // Clone items and add to the end for infinite scrolling
            $originalItems.each(function(index) {
                const $clone = $(this).clone();
                $clone.attr('data-original-index', index);
                $carousel.append($clone);
            });
            
            // Fade in carousel
            $carousel.css('opacity', '1');
            
            // Initialize with the first item active
            updateActiveItem(0, true);
            
            // Setup event listeners for all items
            setupItemListeners();
        }, 300);
    }
    
    // Setup event listeners for carousel items
    function setupItemListeners() {
        $carousel.find('.download-item').each(function(domIndex) {
            $(this).on('click', function() {
                if (isTransitioning) return;
                
                // Use the actual DOM index of the clicked item
                updateActiveItem(domIndex, false);
                
                // Update currentIndex to match
                currentIndex = domIndex;
                
                // Reset autoplay timer
                startAutoplay();
            });
        });
    }
    
    // Data for each item
    const data = [
        {
            title: "PETRONAS 2024 Integrated Report",
            image: "assets/images/integrated-report-2024/download-pir.png",
            downloadUrl: "./assets/pdf/by-section/PETRONAS-Integrated-Report-2024.pdf",
            downloadFilename: "PETRONAS-Integrated-Report-2024.pdf"
        },
        {
            title: "PETRONAS 2024 Audited Financial Statements",
            image: "assets/images/integrated-report-2024/download-audit.png",
            downloadUrl: "assets/pdf/PIR%202024_Audited%20Financial%20Statements.pdf",
            downloadFilename: "PETRONAS-Audited-Financial-Statements-2024.pdf"
        },
        {
            title: "GHG Emissions Data Independent Assurance Statements",
            image: "assets/images/integrated-report-2024/download-pir.png",
            downloadUrl: "https://www.petronas.com/download-pir2024",
            downloadFilename: "GHG-Emissions-Data-Independent-Assurance-Statements.pdf"
        },
        {
            title: "Selected Sustainability Topics Independence Assurance Statement",
            image: "assets/images/integrated-report-2024/download-sustainability.png",
            downloadUrl: "./assets/pdf/Selected-Sustainability-Topics-Independence-Assurance Statement_PIR2024.pdf",
            downloadFilename: "Selected-Sustainability-Topics-Independence-Assurance Statement_PIR2024.pdf"
        },
        {
            title: "PETRONAS Activity Outlook 2025-2027",
            image: "assets/images/integrated-report-2024/download-pao.png",
            downloadUrl: "./assets/pdf/PETRONAS%20Activity%20Outlook%202025-2027.pdf",
            downloadFilename: "PETRONAS-Activity-Outlook-2025-2027.pdf"
        }
        
    ];
    
    let currentIndex = 0;
    let autoplayTimer;
    let isTransitioning = false;
    let itemCount = data.length;
    
    // Add transition to carousel
    $carousel.css('transition', 'opacity 0.3s ease-out');
    
    // Initialize cloning with fade
    cloneItemsWithFade();
    
    // Function to calculate dimensions
    function calculateDimensions() {
        const $firstItem = $carousel.find('.download-item').first();
        const itemWidth = $firstItem.outerWidth();
        
        // Fine-tune the spacing to ensure precise positioning
        const gap = isMobile ? 15 : 30;
        return itemWidth + gap;
    }
    
    // Function to update the active item with fade transitions
    function updateActiveItem(index, skipAnimation = false) {
        if (isTransitioning && !skipAnimation) return;
        isTransitioning = true;
        
        // Get the real index (for data array)
        const realIndex = parseInt($carousel.find('.download-item').eq(index).attr('data-original-index'));
        
        // Update active class
        $carousel.find('.download-item').removeClass('active');
        $carousel.find('.download-item').eq(index).addClass('active');
        
        // Fade out title and active card contents
        if (!skipAnimation) {
            $carouselTitle.addClass('fade-out');
            
            const $currentImg = $activeCard.find('img');
            const $currentText = $activeCard.find('.text');
            
            $currentImg.addClass('fade-out');
            $currentText.addClass('fade-out');
            
            setTimeout(() => {
                updateContent(realIndex);
                
                const $newImg = $activeCard.find('img');
                const $newText = $activeCard.find('.text');
                
                $carouselTitle.removeClass('fade-out').addClass('fade-in');
                $newImg.removeClass('fade-out').addClass('fade-in');
                $newText.removeClass('fade-out').addClass('fade-in');
                
                setTimeout(() => {
                    $carouselTitle.removeClass('fade-in');
                    $newImg.removeClass('fade-in');
                    $newText.removeClass('fade-in');
                }, 300);
            }, 300);
        } else {
            updateContent(realIndex);
        }
        
        // Position carousel with specific adjustments for mobile
        const itemTotalWidth = calculateDimensions();
        let position;
        
        if (isMobile) {
            // For mobile, calculate position based on item width and active state
            const inactiveWidth = 210; // Width of inactive item
            const activeWidth = 250;   // Width of active item
            
            // Base position calculation
            position = index * itemTotalWidth;
            
            // Adjust for second item specifically
            if (realIndex === 1) {
                position -= 35; // Fine-tuned offset for second item
            }
        } else {
            // Desktop positioning
            position = index * itemTotalWidth;
        }
        
        if (skipAnimation) {
            $carousel.css('transition', 'none');
        } else {
            $carousel.css('transition', 'transform 0.5s ease-in-out');
        }
        
        $carousel.css('transform', `translateX(-${position}px)`);
        
        // Handle loop transition
        if (index >= itemCount && !skipAnimation) {
            setTimeout(() => {
                $carousel.css('transition', 'none');
                const newIndex = index % itemCount;
                
                // Apply the same adjustments for the looped position
                let newPosition;
                
                if (isMobile) {
                    const newRealIndex = parseInt($carousel.find('.download-item').eq(newIndex).attr('data-original-index'));
                    
                    // Base position calculation
                    newPosition = newIndex * itemTotalWidth;
                    
                    // Adjust for second item specifically
                    if (newRealIndex === 1) {
                        newPosition -= 35; // Same adjustment as above
                    }
                } else {
                    newPosition = newIndex * itemTotalWidth;
                }
                
                $carousel.css('transform', `translateX(-${newPosition}px)`);
                currentIndex = newIndex;
                
                $carousel.find('.download-item').removeClass('active');
                $carousel.find('.download-item').eq(newIndex).addClass('active');
                
                $carousel[0].offsetHeight; // Force reflow
                $carousel.css('transition', 'transform 0.5s ease-in-out');
                isTransitioning = false;
            }, 500);
        } else {
            setTimeout(() => {
                isTransitioning = false;
            }, skipAnimation ? 0 : 500);
        }
    }
    
    // Function to update content without animations
    function updateContent(realIndex) {
        // Update fixed active card
        $activeCard.html(`
            <img src="${data[realIndex].image}" alt="${data[realIndex].title}">
            <div class="text">${data[realIndex].title}</div>
        `);
        
        // Update title
        $carouselTitle.text(data[realIndex].title);
        
        // Update download button with data attributes for file download
        $downloadButton.attr({
            'href': data[realIndex].downloadUrl,
            'data-download-file': data[realIndex].downloadUrl,
            'data-download-filename': data[realIndex].downloadFilename
        });
    }
    
    // Function to go to the next item
    function goToNext() {
        if (isTransitioning) return;
        
        // Smooth transition to next item
        currentIndex++;
        
        // If we're at the end of original items, prepare to loop
        if (currentIndex >= itemCount * 2) {
            // Instead of jumping directly to 0, we'll handle this in updateActiveItem
            currentIndex = itemCount; // Go to the first clone set
        }
        
        updateActiveItem(currentIndex);
    }
    
    // Function to start autoplay
    function startAutoplay() {
        stopAutoplay();
        autoplayTimer = setInterval(goToNext, 3000);
    }
    
    // Function to stop autoplay
    function stopAutoplay() {
        if (autoplayTimer) {
            clearInterval(autoplayTimer);
        }
    }
    
    // Handle hover on carousel
    $carousel.on({
        mouseenter: stopAutoplay,
        mouseleave: startAutoplay
    });
    
    // Handle download button click
    $downloadButton.on('click', function(event) {
        event.preventDefault();
        const url = $(this).attr('data-download-file');
        const filename = $(this).attr('data-download-filename');
        
        // Check if the URL is an external link (starts with http or https)
        if (url.startsWith('http://') || url.startsWith('https://')) {
            // For external links, just open in a new tab
            window.open(url, '_blank');
        } else {
            // For local files, try to use saveAs if available
            if (typeof saveAs === 'function') {
                saveAs(url, filename);
            } else {
                // Direct download fallback
                const a = document.createElement('a');
                a.href = url;
                a.download = filename || '';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            }
        }
    });
    
    // Handle mobile scroll for carousel wrapper
    if (isMobile) {
        // Add snap scrolling behavior
        $wrapper.css('scrollSnapType', 'x mandatory');
        
        // Add scroll snap align to items
        $carousel.find('.download-item').css('scrollSnapAlign', 'center');
        
        // Set initial scroll position to center the first item
        setTimeout(() => {
            const $firstItem = $carousel.find('.download-item').first();
            const itemRect = $firstItem[0].getBoundingClientRect();
            const wrapperRect = $wrapper[0].getBoundingClientRect();
            
            // Calculate the scroll position to center the item
            const scrollLeft = (itemRect.width - wrapperRect.width) / 2;
            
            // Set initial scroll position
            $wrapper.scrollLeft(scrollLeft > 0 ? scrollLeft : 0);
        }, 500);
        
        $wrapper.on('scroll', function() {
            // Find the item closest to the center of the wrapper
            const wrapperRect = $wrapper[0].getBoundingClientRect();
            const wrapperCenter = wrapperRect.left + wrapperRect.width / 2;
            
            let closestItem = null;
            let closestDistance = Infinity;
            
            // Consider all items for mobile scroll detection
            $carousel.find('.download-item').each(function(index) {
                const itemRect = this.getBoundingClientRect();
                const itemCenter = itemRect.left + itemRect.width / 2;
                const distance = Math.abs(wrapperCenter - itemCenter);
                
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestItem = index;
                }
            });
            
            if (closestItem !== null && closestItem !== currentIndex) {
                // Stop autoplay while user is scrolling
                stopAutoplay();
                
                currentIndex = closestItem;
                
                // Only update the active state and content, not the scroll position
                // to avoid fighting with the user's scroll
                $carousel.find('.download-item').removeClass('active');
                $carousel.find('.download-item').eq(currentIndex).addClass('active');
                
                // Get the real index for data
                const realIndex = parseInt($carousel.find('.download-item').eq(currentIndex).attr('data-original-index'));
                
                // Update content without scrolling
                updateContent(realIndex);
                
                // Restart autoplay after a delay
                setTimeout(startAutoplay, 3000);
            }
        });
    }
    
    // Start autoplay
    startAutoplay();
    
    // Handle window resize
    $(window).on('resize', function() {
        // Check if we crossed the mobile/desktop threshold
        const wasIsMobile = isMobile;
        const newIsMobile = $(window).width() <= 768;
        
        // If we crossed the threshold, reload the page to apply the correct layout
        if (wasIsMobile !== newIsMobile) {
            location.reload();
            return;
        }
        
        // Update position without animation
        updateActiveItem(currentIndex, true);
    });
    
    // Add initial fade-in for the active card contents
    setTimeout(() => {
        const $initialImg = $activeCard.find('img');
        const $initialText = $activeCard.find('.text');
        
        $initialImg.addClass('fade-in');
        $initialText.addClass('fade-in');
        $carouselTitle.addClass('fade-in');
        
        // Remove fade-in class after animation completes
        setTimeout(() => {
            $initialImg.removeClass('fade-in');
            $initialText.removeClass('fade-in');
            $carouselTitle.removeClass('fade-in');
        }, 300);
    }, 300);
});