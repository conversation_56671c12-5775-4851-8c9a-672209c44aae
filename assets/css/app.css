@charset "UTF-8";
:root {
  --text-theme-color: #00a19c;
  --text-secondary-color: #763f98;
  --text-white-color: #ffffff;
  --text-black-color: #423434;
  --border-theme-color: #00a19c;
  --border-secondary-color: #763f98;
  --border-black-color: #4d4d4f;
  --border-white-color: #ffffff;
  --background-theme-color: #00a19c;
  --background-light-theme-color: #00a19c;
  --background-secondary-color: #763f98;
  --background-white-color: #ffffff;
  --background-light-grey-color: #f5f5f5;
  --navbar-text-color: var(--text-black-color);
  --navbar-theme-color: #00a19c;
  --p-font-size: 16px;
  --p-line-height: 20px;
  --h1-font-size: 70px;
  --h1-line-height: 74px;
  --h2-font-size: 55px;
  --h2-line-height: 59px;
  --h3-font-size: 50px;
  --h3-line-height: 54px;
  --h4-font-size: 45px;
  --h4-line-height: 49px;
  --h5-font-size: 40px;
  --h5-line-height: 44px;
  --h6-font-size: 20px;
  --h6-line-height: 24px;
  --button-font-size: 18px;
  --button-line-height: 22px;
  --swiper-pagination-left: 20px;
  --swiper-pagination-right: auto;
  --swiper-pagination-bullet-inactive-color: #00a19c;
  --swiper-pagination-bullet-inactive-opacity: 1;
  --swiper-pagination-color: #00a19c;
  --swiper-pagination-bullet-width: 10px;
  --swiper-pagination-bullet-height: 10px;
  --swiper-pagination-bullet-vertical-gap: 20px;
  --swiper-pagination-bullet-border-color: var(--border-theme-color);
  --swiper-pagination-bullet-horizontal-gap: 8px;
  --hamburger-line: #000000;
  --doc-height: 100%;
  --bs-link-color: #0079c0;
  --bs-link-hover-color: #005283;
  --animate-duration: 1300ms;
  --animate-delay: 0.9s;
}
@media (min-width: 640px) {
  :root {
    --swiper-pagination-left: 40px;
    --swiper-pagination-bullet-width: 14px;
    --swiper-pagination-bullet-height: 14px;
    --h3-font-size: 48px;
    --h3-line-height: 53px;
    --h4-font-size: 40px;
    --h4-line-height: 45px;
    --h5-font-size: 40px;
    --h5-line-height: 48px;
    --button-font-size: 20px;
    --button-line-height: 24px;
  }
}
@media screen and (min-device-width: 1200px) and (max-device-width: 1600px) and (-webkit-min-device-pixel-ratio: 2) and (-webkit-min-device-pixel-ratio: 2), screen and (min-device-width: 1200px) and (max-device-width: 1600px) and (-webkit-min-device-pixel-ratio: 2) and (min-resolution: 192dpi) {
  :root {
    --h1-font-size: 94px;
    --h1-line-height: 99px;
    --h2-font-size: 61px;
    --h2-line-height: 65px;
  }
}
@media screen and (min-width: 1440px) {
  :root {
    --h1-font-size: 136px;
    --h1-line-height: 140px;
    --h2-font-size: 90px;
    --h2-line-height: 94px;
    --h3-font-size: 50px;
    --h3-line-height: 54px;
    --h4-font-size: 45px;
    --h4-line-height: 49px;
    --h5-font-size: 40px;
    --h5-line-height: 44px;
    --h6-font-size: 24px;
    --h6-line-height: 28px;
    --p-font-size: 20px;
    --p-line-height: 24px;
  }
}

body.is-open-content,
body.bullet-white {
  --swiper-pagination-bullet-inactive-color: #ffffff;
  --swiper-pagination-bullet-border-color: var(--border-white-color);
  --navbar-text-color: #ffffff;
  --hamburger-line: #ffffff;
}

@media (max-width: 991px) {
  body.bullet-white.nav-is-open {
    --navbar-text-color: #222222;
    --hamburger-line: #000000;
  }
}
body.nav-is-open .navbar::after {
  display: none !important;
}

@media (max-width: 991px) {
  body.nav-is-open .navbar {
    --navbar-text-color: #222222;
  }
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-100.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-100.woff") format("woff"), url("./../../assets/fonts/museo-sans-100.otf") format("opentype");
  font-display: auto;
  font-style: normal;
  font-weight: 100;
  font-stretch: normal;
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-italic-100.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-italic-100.woff") format("woff"), url("./../../assets/fonts/museo-sans-italic-100.otf") format("opentype");
  font-display: auto;
  font-style: italic;
  font-weight: 100;
  font-stretch: normal;
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-300.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-300.woff") format("woff"), url("./../../assets/fonts/museo-sans-300.otf") format("opentype");
  font-display: auto;
  font-style: normal;
  font-weight: 300;
  font-stretch: normal;
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-italic-300.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-italic-300.woff") format("woff"), url("./../../assets/fonts/museo-sans-italic-300.otf") format("opentype");
  font-display: auto;
  font-style: italic;
  font-weight: 300;
  font-stretch: normal;
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-500.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-500.woff") format("woff"), url("./../../assets/fonts/museo-sans-500.otf") format("opentype");
  font-display: auto;
  font-style: normal;
  font-weight: 500;
  font-stretch: normal;
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-italic-500.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-italic-500.woff") format("woff"), url("./../../assets/fonts/museo-sans-italic-500.otf") format("opentype");
  font-display: auto;
  font-style: italic;
  font-weight: 500;
  font-stretch: normal;
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-700.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-700.woff") format("woff"), url("./../../assets/fonts/museo-sans-700.otf") format("opentype");
  font-display: auto;
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-700.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-700.woff") format("woff"), url("./../../assets/fonts/museo-sans-700.otf") format("opentype");
  font-display: auto;
  font-style: italic;
  font-weight: 700;
  font-stretch: normal;
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-900.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-900.woff") format("woff"), url("./../../assets/fonts/museo-sans-900.otf") format("opentype");
  font-display: auto;
  font-style: normal;
  font-weight: 900;
  font-stretch: normal;
}
@font-face {
  font-family: "Museo Sans";
  src: url("./../../assets/fonts/museo-sans-italic-900.woff2") format("woff2"), url("./../../assets/fonts/museo-sans-italic-900.woff") format("woff"), url("./../../assets/fonts/museo-sans-italic-900.otf") format("opentype");
  font-display: auto;
  font-style: italic;
  font-weight: 900;
  font-stretch: normal;
}
html {
  scroll-behavior: auto !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-text-size-adjust: none;
  -moz-text-size-adjust: none;
  text-size-adjust: none;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  background: var(--background-white-color);
  color: var(--text-black-color);
  height: 100vh; /* fallback for Js load */
  height: var(--doc-height);
  padding-right: 0 !important;
}
body .home-content {
  transition: all ease 0.5s;
  transform: translateY(0px);
}

.opacity-animation-0 {
  opacity: 0;
}

body.open {
  pointer-events: all;
}
body.open .home-content {
  transform: translateY(-100px);
  overflow: visible;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
}
@media screen and (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}
@media screen and (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}
@media screen and (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}
@media screen and (min-width: 1100px) and (max-width: 1300px) {
  .container {
    max-width: 1100px;
  }
}
@media screen and (min-width: 1301px) and (max-width: 1400px) {
  .container {
    max-width: 1200px;
  }
}
@media screen and (min-width: 1401px) and (max-width: 1599px) {
  .container {
    max-width: 1280px;
  }
}
@media screen and (min-width: 1600px) and (max-width: 1899px) {
  .container {
    max-width: 1400px;
  }
}
@media screen and (min-width: 1900px) {
  .container {
    max-width: 1525px;
  }
}

.mr-30 {
  margin-right: 30px;
}

@media screen and (min-width: 992px) {
  .hide-on-desktop {
    display: none !important;
  }
}
body .container,
.container .row div[class*=col-],
.container .row div[class*=col-md-],
.container .row div[class*=col-lg-],
body .container-fluid,
.container-fluid .row div[class*=col-],
.container-fluid .row div[class*=col-md-],
.container-fluid .row div[class*=col-lg-] {
  padding-right: 25px;
  padding-left: 25px;
}

.row {
  margin-left: -25px;
  margin-right: -25px;
}

.gx-0 {
  margin-left: 0px;
  margin-right: 0px;
}
.gx-0 div[class*=col-] {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

body .px-sm-2 {
  padding-left: 15px;
  padding-right: 15px;
}
@media (min-width: 576px) {
  body .px-sm-2 {
    padding-left: 25px;
    padding-right: 25px;
  }
}

.gx-sm-2 {
  margin-left: 5px;
  margin-right: 5px;
}
@media (min-width: 576px) {
  .gx-sm-2 {
    margin-left: 10px;
    margin-right: 10px;
  }
}
.gx-sm-2 div[class*=col-] {
  padding-left: 5px !important;
  padding-right: 5px !important;
}
@media (min-width: 576px) {
  .gx-sm-2 div[class*=col-] {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }
}

button,
a,
h1,
h2,
h3,
h4,
h5,
h6,
span,
input,
p {
  font-family: "Museo Sans", sans-serif;
  margin-bottom: unset;
  letter-spacing: 0px;
}

span.highlight {
  color: var(--text-theme-color);
}

h1 {
  font-size: var(--h1-font-size);
  line-height: var(--h1-line-height);
}

h2 {
  font-size: var(--h2-font-size);
  line-height: var(--h2-line-height);
}

h3 {
  font-size: var(--h3-font-size);
  line-height: var(--h3-line-height);
}

h4 {
  font-size: var(--h4-font-size);
  line-height: var(--h4-line-height);
}

h5 {
  font-size: var(--h5-font-size);
  line-height: var(--h5-line-height);
}

h6 {
  font-size: var(--h6-font-size);
  line-height: var(--h6-line-height);
}

p {
  font-size: var(--p-font-size);
  line-height: var(--p-line-height);
}

input {
  font-size: 16px;
  line-height: 24px;
}

.fw-300 {
  font-weight: 300;
}

.fw-500 {
  font-weight: 500;
}

.fw-700 {
  font-weight: 700;
}

.fw-900 {
  font-weight: 900;
}

@media (max-width: 575px) {
  body.nav-is-open .navbar {
    background-color: #ffffff;
  }
}
/* iPad Portrait */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: portrait) {
  /* CSS styles here */
  body.nav-is-open .navbar {
    background-color: #ffffff;
  }
  .ipad-portrait-col-6 {
    flex: 0 0 auto;
    width: 80%;
  }
  .ipad-portrait-offset-0 {
    margin-left: 0px;
  }
  .ipad-portrait-col-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .ipad-portrait-flex-align-center {
    display: flex;
    align-items: center;
  }
}
/* iPad Landscape */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: landscape) {
  /* CSS styles here */
  body.nav-is-open .navbar {
    background-color: #ffffff;
  }
  .ipad-landscape-offset-1 {
    margin-left: 8.33333%;
  }
  .ipad-landscape-col-11 {
    flex: 0 0 auto;
    width: 91.66667%;
  }
}
/* iPad Air Portrait */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (min-device-height: 1024px) and (max-device-height: 1366px) and (orientation: portrait) {
  /* CSS styles here */
  body.nav-is-open .navbar {
    background-color: #ffffff;
  }
  .ipad-portrait-col-6 {
    flex: 0 0 auto;
    width: 80%;
  }
  .ipad-portrait-offset-0 {
    margin-left: 0px;
  }
  .ipad-portrait-col-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .ipad-portrait-flex-align-center {
    display: flex;
    align-items: center;
  }
}
/* iPad Air Landscape */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (min-device-height: 1024px) and (max-device-height: 1366px) and (orientation: landscape) {
  /* CSS styles here */
  body.nav-is-open .navbar {
    background-color: #ffffff;
  }
  .ipad-landscape-offset-1 {
    margin-left: 8.33333%;
  }
  .ipad-landscape-col-11 {
    flex: 0 0 auto;
    width: 91.66667%;
  }
}
/* iPad Pro Portrait */
@media only screen and (min-device-width: 1024px) and (max-device-width: 1366px) and (orientation: portrait) {
  /* CSS styles here */
  body.nav-is-open .navbar {
    background-color: #ffffff;
  }
  .ipad-portrait-col-6 {
    flex: 0 0 auto;
    width: 80%;
  }
  .ipad-portrait-offset-0 {
    margin-left: 0px;
  }
  .ipad-portrait-col-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .ipad-portrait-flex-align-center {
    display: flex;
    align-items: center;
  }
}
/* iPad Pro Landscape */
@media only screen and (min-device-width: 1024px) and (max-device-width: 1366px) and (orientation: landscape) {
  /* CSS styles here */
  body.nav-is-open .navbar {
    background-color: #ffffff;
  }
  .ipad-landscape-offset-1 {
    margin-left: 8.33333%;
  }
  .ipad-landscape-col-11 {
    flex: 0 0 auto;
    width: 91.66667%;
  }
}
.text-animation {
  position: relative;
}

.line {
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  --highlight-offset: 0%;
  /* background-image: linear-gradient(90deg, var(--text-black-color) var(--highlight-offset), #9b9b9b var(--highlight-offset)); */
  display: inline;
}

.btn:disabled {
  opacity: 1;
}

.btn-more-detail {
  background-color: var(--background-theme-color);
  border-radius: 30px;
  color: var(--text-white-color);
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  padding-left: 0.55rem;
  padding-right: 0.95rem;
  box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.5);
  z-index: 99;
  transition: all ease 0.5s;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.btn-more-detail img {
  width: 30px;
  height: 30px;
  -o-object-fit: contain;
     object-fit: contain;
  margin-right: 0.325rem;
  transition: all ease 0.5s;
}
.btn-more-detail span {
  max-width: 120px;
  transition: all ease 0.5s;
  display: block;
  overflow: hidden;
  white-space: nowrap;
}

.btn-more-detail.open {
  padding-left: 0rem;
  padding-right: 0rem;
  border-radius: 50px;
  width: 60px;
  height: 60px;
}
@media (min-width: 576px) {
  .btn-more-detail.open {
    width: 80px;
    height: 80px;
  }
}
.btn-more-detail.open img {
  margin-right: 0px;
  width: 20px;
  height: 20px;
}
.btn-more-detail.open span {
  width: 0px;
}

.btn-more-detail:hover,
.btn-more-detail:focus {
  color: var(--text-white-color);
  box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.5);
}

.btn-view-full-message {
  font-weight: 500;
  font-size: 1.125rem;
  line-height: 1.375rem;
  text-decoration: none;
  color: var(--text-theme-color);
  box-shadow: unset;
  padding-left: 0px;
  padding-right: 0px;
  margin-left: -7px;
}
.btn-view-full-message img {
  width: 40px;
  height: 40px;
  -o-object-fit: contain;
     object-fit: contain;
  margin-right: 0.325rem;
}

.btn-view-full-message:hover,
.btn-view-full-message:focus {
  color: var(--text-theme-color);
  box-shadow: unset;
}

.btn-learn-more {
  font-weight: 500;
  font-size: 1.125rem;
  line-height: 1.375rem;
  text-decoration: none;
  color: var(--text-theme-color);
  box-shadow: unset;
  padding-left: 0px;
  padding-right: 0px;
  margin-left: -7px;
  padding-left: 5px;
}
.btn-learn-more img {
  width: 40px;
  height: 40px;
  -o-object-fit: contain;
     object-fit: contain;
  margin-right: 0.325rem;
}

.btn-learn-more:hover,
.btn-learn-more:focus {
  color: var(--text-theme-color);
  box-shadow: unset;
}

.btn-view-more {
  font-weight: 500;
  font-size: 1.125rem;
  line-height: 1.375rem;
  text-decoration: none;
  color: var(--text-theme-color);
  box-shadow: unset;
  padding-left: 0px;
  padding-right: 0px;
  margin-left: -7px;
}
.btn-view-more img {
  width: 40px;
  height: 40px;
  -o-object-fit: contain;
     object-fit: contain;
  margin-right: 0.325rem;
}

.btn-view-more:hover,
.btn-view-more:focus {
  color: var(--text-theme-color);
  box-shadow: unset;
}

html .btn-more-detail-wrapper {
  position: fixed;
  left: 50%;
  bottom: 10%;
  transform: translateX(-50%);
  transition: bottom ease 0.5s;
  z-index: 100;
}

.text-theme-color {
  color: var(--text-theme-color);
}

.text-black-color {
  color: var(--text-black-color);
}

.accordion-button:not(.collapsed)::after {
  background-image: url("./../../assets/images/global/show-less.png");
  transform: rotate(-180deg);
}

.accordion-button::after {
  flex-shrink: 0;
  width: 1.05rem;
  height: 1.05rem;
  margin-left: auto;
  content: "";
  background-image: url("./../../assets/images/global/show-more.png");
  background-repeat: no-repeat;
  background-size: 1.05rem;
  transition: transform 0.2s ease-in-out;
}

.accordion-item {
  background: transparent;
  border: unset;
  border-radius: unset !important;
  border-top: 1px solid var(--border-black-color) !important;
}

.accordion-button {
  color: var(--text-black-color);
  background: transparent;
  box-shadow: unset;
  padding-left: 0px;
  box-shadow: unset;
  outline: unset;
  font-weight: 700;
  font-size: var(--button-font-size);
  line-height: var(--button-line-height);
}
.accordion-button img {
  width: 25px;
  height: 25px;
  -o-object-fit: cover;
     object-fit: cover;
  margin-right: 20px;
}

.accordion-body {
  padding-left: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
}
@media (min-width: 576px) {
  .accordion-body {
    padding-right: inherit;
    padding-bottom: 0px;
  }
}

.accordion-button:focus,
.accordion-button:hover {
  box-shadow: unset;
  outline: unset;
}

.accordion-button:not(.collapsed) {
  color: var(--text-black-color);
  background: transparent;
  box-shadow: unset;
}

.mb-100 {
  margin-bottom: 100px;
}

.swiper-next,
.swiper-prev {
  background: unset;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 45px;
  height: 45px;
  border: unset;
  outline: unset;
  box-shadow: unset;
}

.swiper-prev + .swiper-next {
  margin-left: 5px;
}

.swiper-next {
  background-image: url("./../../assets/images/global/swiper-navigation-next-active.png");
}

.swiper-next.disabled {
  background-image: url("./../../assets/images/global/swiper-navigation-next-inactive.png");
}

.swiper-prev {
  background-image: url("./../../assets/images/global/swiper-navigation-prev-active.png");
}

.swiper-prev.disabled {
  background-image: url("./../../assets/images/global/swiper-navigation-prev-inactive.png");
}

.btn-theme, .btn-secondary {
  /* background-color: var(--background-theme-color) !important; */
  background-color: var(--background-light-theme-color) !important;
  border-radius: 25px;
  border: unset;
  margin-top: var(--btn-margin-top, 0px);
  text-align: left;
  color: var(--text-white-color);
  padding: var(--padding-y, 12px) 0px;
  padding-left: var(--padding-x, 60px);
  padding-right: var(--padding-x, 40px);
  --btn-hover-color: var(--text-white-color);
  font-size: var(--button-font-size);
  line-height: var(--button-line-height);
  position: relative;
  touch-action: manipulation;
}
.btn-theme:before, .btn-secondary:before {
  content: "";
  background-image: url("./../../assets/images/global/white-button.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position-x: 10px;
  background-position-y: center;
  max-width: 40px;
  width: 100%;
  height: 30px;
  top: 50%;
  transform: translateY(-50%);
  left: 0px;
  position: absolute;
  display: block;
}
.btn-theme:focus, .btn-secondary:focus, .btn-theme:hover, .btn-secondary:hover {
  border: unset !important;
  outline: unset !important;
  box-shadow: unset !important;
  color: var(--btn-hover-color);
}

.btn-secondary {
  background-color: var(--background-secondary-color) !important;
}

.timeline {
  display: flex;
  gap: 20px;
  flex-flow: column;
}
@media (min-width: 640px) {
  .timeline {
    flex-flow: row;
  }
}
.timeline.animated .tl-item {
  clip-path: inset(0% round 20px);
  opacity: 1;
}

.tl-item {
  opacity: 0;
  clip-path: inset(100% round 20px);
  transition: all ease 1s;
  transform: translate3d(0, 0, 0);
  position: relative;
  height: 600px;
  color: var(--text-white-color);
  overflow: hidden;
}
@media (min-width: 640px) and (max-width: 1439px) {
  .tl-item {
    height: 400px;
  }
}
.tl-item.in-swiper {
  clip-path: inset(0% round 20px);
  opacity: 1;
}
.tl-item:before, .tl-item:after {
  transform: translate3d(0, 0, 0);
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.tl-item:after {
  opacity: 1;
  transition: opacity 0.5s ease;
}
.tl-item:before {
  /* background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgb(0, 0, 0) 75%); */
  z-index: 1;
  /* opacity: 0; */
  opacity: 1;
  transform: translate3d(0, 0, 0) translateY(40%);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

#our-sutainability-journey .tl-item:before {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgb(0, 0, 0) 75%);
  opacity: 1;
}
.tl-item.shadow::before {
  opacity: 1;
}
.tl-item .tl-content {
  transform: translate3d(0, 0, 0) translateY(25px);
  position: relative;
  z-index: 1;
  text-align: center;
  margin: 0 1.618em;
  top: 60%;
  opacity: 0;
}
.tl-item .tl-year {
  position: absolute;
  /* top: 95%; */
  /* left: 5%; */
  right: 5%;
  /* transform: translateX(0%) translateY(-100%); */
  z-index: 1;
  max-width: 440px;
  width: 90%;
  /* text-align: left; */
  transition: all ease 500ms;
  display: flex;
  flex-direction: column;
  gap: 20px;

  transform: translateX(-50%) translateY(-50%);
  left: 50%;
  top: 36%;
  text-align: center;
}

#our-sutainability-journey .tl-item .tl-year {
  position: absolute;
  top: 95%;
  left: 5%;
  right: 5%;
  transform: translateX(0%) translateY(-100%);
  z-index: 1;
  max-width: 440px;
  width: 90%;
  text-align: left;
  transition: all ease 500ms;
  display: flex;
  flex-direction: column;
  gap: 20px;

  /* transform: translateX(-50%) translateY(-50%);
  left: 50%;
  top: 36%;
  text-align: center; */
}

.tl-item .tl-year .tl-img {
  /* opacity: 0; */
  opacity: 1;
  max-width: 150px;
  width: 100%;
  margin: 0 auto;
  transition: unset;
}
@media (max-width: 1479px) {
  .tl-item .tl-year .tl-img {
    max-width: 100px;
  }
}
.tl-item .tl-year .tl-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-height: 150px;
}
.tl-item .tl-year h4,
.tl-item .tl-year p {
  font-family: "Museo Sans";
  color: var(--text-white-color);
}
.tl-item .tl-year h4 {
  --h4-font-size: 35px;
  --h4-line-height: 39px;
  font-weight: 700;
}
@media (max-width: 1479px) {
  .tl-item .tl-year h4 {
    font-size: 25px;
    line-height: 29px;
  }
}
.tl-item .tl-year.animation-2 {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-height: 150px;
}

#our-sutainability-journey .tl-item .tl-year.animation-2 {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-height: 150px;
}

.tl-item .tl-year.animation-2 h4 {
  display: block;
}
.tl-item .tl-year.animation-2 p {
  display: none;
}
@media (min-width: 640px) {
  .tl-item {
    width: 33.33333%;
  }
  .tl-item:hover {
    width: 39.99999% !important;
  }
  .tl-item:hover:after {
    opacity: 0;
  }
  .tl-item:hover:before {
    opacity: 1;
    transform: translate3d(0, 0, 0) translateY(0);
    transition: opacity 1s ease, transform 1s ease 0.25s;
  }
  .tl-item:hover .tl-content {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.75s ease 0.5s;
  }
  /* .tl-item:hover .tl-year {
    transform: translateX(-50%) translateY(-50%);
    left: 50%;
    top: 36%;
    text-align: center;
  } */
  /* .tl-item:hover .tl-year .tl-img {
    opacity: 1;
    transition-property: opacity;
    transition-timing-function: ease;
    transition-duration: 500ms;
    transition-delay: 500ms;
  } */

  #our-sutainability-journey .tl-item:hover .tl-year {
    /* transform: translateX(-50%) translateY(-50%);
    left: 50%;
    top: 36%;
    text-align: center; */
    top: 88%;
    left: 5%;
    transform: translateX(0%) translateY(-100%);
    text-align: left;
  }

  .tl-item:hover .tl-year.animation-2 {
    top: 88%;
    left: 5%;
    transform: translateX(0%) translateY(-100%);
    text-align: left;
  }
}
@media (min-width: 640px) and (min-width: 1600px) {
  .tl-item:hover .tl-year.animation-2 {
    top: 88%;
  }
}
@media (min-width: 640px) {
  .tl-item:hover .tl-year.animation-2 p {
    display: block;
  }
}

/* .show-mobile {
  display:none!important;
} */

@media (max-width: 639px) {
  #home #our-purpose .nav-pills .nav-link {
    font-size: 14px!important;
  }

  /* .show-mobile {
    display:block!important;
  } */
  .tl-item .tl-content {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.75s ease 0.5s;
  }
  .tl-item:after {
    opacity: 0;
  }
  .tl-item:before {
    opacity: 1;
    transform: translate3d(0, 0, 0) translateY(0);
    transition: opacity 1s ease, transform 1s ease 0.25s;
  }
}
@media (max-width: 639px) {
  .tl-item .tl-year {
    transform: translateX(-50%) translateY(-50%);
    left: 50%;
    top: 39%;
    text-align: center;
  }
  .tl-item .tl-year .tl-img {
    opacity: 1;
  }
  .tl-item .tl-year.animation-2 {
    /* top: 95%; */
    top: 92%;
    left: 5%;
    right: 5%;
    width: unset;
    transform: translateX(0%) translateY(-100%);
    text-align: left;
  }
  .tl-item .tl-year.animation-2 h4 {
    display: block;
  }
  .tl-item .tl-year.animation-2 p {
    display: block;
  }
}

.tl-bg {
  background: linear-gradient(146deg, rgba(0, 161, 156, 1) 0%, rgba(93, 83, 153, 1) 100%);
  transform: translate3d(0, 0, 0);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-size: cover;
  background-position: center center;
}

.swiper-report .swiper-pagination {
  text-align: left;
}
@media (max-width: 639px) {
  .swiper-report .swiper-pagination {
    text-align: center;
  }
}

.swiper-pagination-bullet {
  transition: background ease 0.5s;
  position: relative;
}

.swiper-pagination-bullet.swiper-pagination-bullet-active::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  border: 1px solid var(--swiper-pagination-bullet-border-color);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  width: calc(var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px)) + 10px);
  height: calc(var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px)) + 10px);
}

.accordion .accordion-item .accordion-header .accordion-button {
  font-family: "Museo Sans";
  font-weight: 500;
  color: var(--text-black-color);
  transition: color ease 1s;
}
.accordion .accordion-item .accordion-header .accordion-button:not(.collapsed) {
  color: var(--text-theme-color);
}
.accordion .accordion-item .accordion-collapse .accordion-body p {
  font-family: "Museo Sans";
  font-weight: 500;
  color: var(--text-black-color);
}

.section-preloader {
  display: block;
  width: 100%;
  background: var(--background-theme-color);
  z-index: 9999;
  position: fixed;
  transform: translateY(0%);
  transition: transform ease 1s;
}
.section-preloader .row {
  min-height: 100vh;
}
.section-preloader .font24 {
  font-size: 24px;
  line-height: 29px;
  color: var(--text-white-color);
}
.section-preloader .preloader {
  display: flex;
  align-items: center;
  width: auto;
}
@media screen and (max-width: 767px) {
  .section-preloader .preloader {
    justify-content: center;
    flex-direction: column;
  }
}
.section-preloader .preloader .preload-img {
  display: block;
  width: 200px;
  height: auto;
  position: relative;
}
@media screen and (min-width: 768px) {
  .section-preloader .preloader .preload-img {
    margin-right: 80px;
  }
}
@media screen and (max-width: 767px) {
  .section-preloader .preloader .preload-img {
    width: 260px;
    margin-bottom: 50px;
  }
}
@media screen and (min-width: 768px) {
  .section-preloader .preloader .preload-img::after {
    content: "";
    display: block;
    position: absolute;
    top: 50%;
    right: -40px;
    width: 2px;
    height: 100%;
    background-color: #ffffff;
    transform: translate(0, -50%);
  }
}
.section-preloader .preloader .preload-title p {
  margin-bottom: 15px;
}
.section-preloader .preloader .preload-title .preload-bar,
.section-preloader .preloader .preload-title .preload-bar::before {
  display: block;
  width: 100%;
  position: relative;
}
.section-preloader .preloader .preload-title .preload-bar::before {
  content: "";
  opacity: 0.4;
  overflow: hidden;
}
.section-preloader .preloader .preload-title .preload-bar::before,
.section-preloader .preloader .preload-title .preload-bar .preload-inner {
  height: 4px;
  background-color: #ffffff;
  border-radius: 4px;
}
.section-preloader .preloader .preload-title .preload-bar .preload-inner {
  position: absolute;
  display: block;
  top: 50%;
  left: 0;
  transform: translate(0, -50%);
  z-index: 1;
  animation-name: bar-render;
  animation-duration: 1s;
  animation-fill-mode: forwards;
  animation-iteration-count: 1;
}
@keyframes bar-render {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.section-preloader.animate_top {
  transform: translateY(-100%);
}

.modal {
  padding-right: 0 !important;
}
.modal .btn-close {
  position: absolute;
  right: 15px;
  top: 30px;
}
.modal .accordion {
  margin-top: 20px;
}
.modal .accordion-item:first-child {
  border-top: unset !important;
}
.modal .accordion-body {
  padding-top: unset;
}
.modal .modal-pdf-max-height {
  max-height: 350px;
  min-height: 350px;
  overflow: auto;
}
.modal .accordion-button:not([data-bs-toggle])::after {
  display: none;
}
.modal a:hover {
  color: var(--text-theme-color) !important;
  text-decoration: underline !important;
}
.modal a:not([href]):hover {
  color: #222222 !important;
  text-decoration: unset !important;
}
.modal .modal-pdf-max-height::-webkit-scrollbar-track,
.modal .modal-pdf-max-height::-webkit-scrollbar-thumb {
  border-radius: 12px;
}
.modal .modal-pdf-max-height::-webkit-scrollbar-track {
  background-color: #d1d1d1;
}
.modal .modal-pdf-max-height::-webkit-scrollbar {
  width: 6px;
}
.modal .modal-pdf-max-height::-webkit-scrollbar-thumb {
  background-color: #727272;
}
.modal .underline {
  position: relative;
}
.modal .underline::after {
  content: "";
  background: #a2a2a2;
  bottom: 0px;
  left: 0px;
  width: 100%;
  height: 1px;
  position: absolute;
}
.modal .theme-green,
.modal .link,
.modal a:hover,
.modal .nav-link:hover,
.modal .cta a,
.modal .cta a p {
  color: #00a19c;
}
.modal a,
.modal .owl-dot,
.modal .owl-nav button,
.modal .owl-nav button img,
.modal .newsletter .form-group .newsletter-email,
.modal a::before,
.modal a::after,
.modal input,
.modal textarea,
.modal select,
.modal option {
  transition: all 0.35s ease;
}
.modal a,
.modal a:hover,
.modal a:focus,
.modal a:visited,
.modal a:active,
.modal .nav-link,
.modal .nav-link:visited,
.modal .nav-link:focus {
  color: #222222;
}
.modal a,
.modal a:focus,
.modal a:hover,
.modal a:active,
.modal a:visited,
.modal button,
.modal button:focus,
.modal button:hover,
.modal button:active,
.modal button:visited,
.modal .btn-close,
.modal .btn-close:hover,
.modal .btn-close:active,
.modal .btn-close:focus,
.modal .btn-close:visited,
.modal .navbar-toggler,
.modal .navbar-toggler:focus,
.modal .navbar-toggler:visited,
.modal .navbar-toggler:hover,
.modal .navbar-toggler:active {
  outline: none;
  text-decoration: none;
  box-shadow: none;
}
.modal p,
.modal a,
.modal span,
.modal li,
.modal input,
.modal textarea,
.modal select,
.modal label,
.modal div,
.modal th,
.modal td,
.modal dt,
.modal dd {
  letter-spacing: 0.05rem;
  font-size: 16px;
  line-height: 24px;
}
@media screen and (min-width: 1600px) {
  .modal .modal-dialog {
    max-width: 1000px;
  }
  .modal .modal-dialog .modal-body {
    width: 850px;
    margin: 0 auto;
    padding: 70px 0 110px;
  }
}
@media screen and (min-width: 992px) and (max-width: 1599px) {
  .modal .modal-dialog {
    max-width: 800px;
  }
  .modal .modal-dialog .modal-body {
    width: 660px;
    margin: 0 auto;
    padding: 50px 0 90px;
  }
}
@media screen and (min-width: 768px) and (max-width: 991px) {
  .modal .modal-dialog {
    max-width: 650px;
  }
  .modal .modal-dialog .modal-body {
    padding: 60px 30px;
  }
  .modal .modal-dialog .modal-body .btn-close {
    right: 25px;
  }
}
@media screen and (min-width: 768px) {
  .modal #downloadModal .modal-title {
    margin-bottom: 30px;
  }
  .modal #downloadModal .modal-bottom .cta {
    margin-top: 70px;
  }
  .modal .btn-close {
    right: -30px;
  }
  .modal .download-list {
    max-height: 350px;
    width: calc(100% + 10px);
    padding-right: 8px;
    padding-bottom: 15px;
  }
  .modal .download-list .download-item {
    padding: 20px 0;
  }
}
@media screen and (max-width: 575px) {
  .modal .hidden-xs {
    display: none !important;
  }
}
@media screen and (max-width: 767px) {
  .modal #downloadModal .modal-title,
  .modal #downloadModal .nav {
    margin-bottom: 20px;
  }
  .modal #downloadModal .modal-bottom .cta {
    margin-top: 50px;
  }
  .modal .modal-body {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .modal .btn-close {
    padding: 0;
    width: 20px;
    height: 20px;
    background-size: contain;
    right: 25px;
  }
  .modal .download-list .download-item {
    padding: 30px 0;
  }
  .modal .download-list .download-item .download-description {
    margin-bottom: 15px;
    margin-bottom: 0;
    padding-right: 15px;
  }
  .modal .download-list .download-item div[class*=download-],
  .modal .download-list .download-item .col-nav {
    width: 100%;
    position: relative;
  }
  .modal .download-list .download-item .download-formats {
    width: auto !important;
    padding: 15px 0;
    padding-right: 5px;
  }
}

@media screen and (min-width: 401px) and (max-width: 767px) {
  .row > [class*=col-],
  .navbar .container,
  .mainBanner,
  .modal .modal-body {
    padding-left: 30px;
    padding-right: 30px;
  }
}
@media screen and (max-width: 400px) {
  .row > [class*=col-],
  .navbar .container,
  .mainBanner,
  .modal .modal-body {
    padding-left: 25px;
    padding-right: 25px;
  }
}
.modal .modal-content {
  border-radius: 1.5rem;
}
.modal .modal .modal-content {
  border-radius: 1rem;
}
.modal .download-list {
  flex-wrap: wrap;
}
.modal .download-list .download-item {
  width: 100%;
}
.modal .download-list .download-item.parent-item {
  padding: 12px 0;
  position: relative;
}
.modal .download-list .download-item.parent-item:last-child::after {
  content: "";
  width: 90%;
  height: 1px;
  background: #a2a2a2;
  position: absolute;
  left: 0px;
  bottom: 0px;
}
.modal .download-list .download-item.parent-item::before {
  content: "";
  width: 90%;
  height: 1px;
  background: #a2a2a2;
  position: absolute;
  left: 0px;
  top: 0px;
}
.modal .download-list .download-item .item-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}
.modal .download-list .download-item a:hover {
  text-decoration: underline;
}
.modal .download-list .download-item.parent-item .submenu {
  display: block;
  list-style-type: disc;
  margin-block-start: 0em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  padding-inline-start: 20px;
}
.modal .download-list .download-item.parent-item .submenu .child-item {
  position: relative;
  padding: 5px 0;
}
.modal .download-list .download-item.parent-item .submenu .child-item.title::before {
  display: none;
}
.modal .download-list .download-item.parent-item .submenu .child-item.title {
  margin-left: -25px;
}
.modal .download-list .download-item.parent-item .submenu .child-item.title .download-description a {
  pointer-events: none;
  font-size: 18px;
}
.modal .download-list .download-item.parent-item .submenu .child-item .download-description a {
  font-size: 16px;
}
.modal .download-list .download-item .download-description a img {
  width: 15px;
  height: 15px;
  margin-left: 8px;
}
.modal .download-list {
  overflow-y: scroll;
}
.modal .download-list::-webkit-scrollbar-track,
.modal .download-list::-webkit-scrollbar-thumb {
  border-radius: 12px;
}
.modal .download-list::-webkit-scrollbar-track {
  background-color: #d1d1d1;
}
.modal .download-list::-webkit-scrollbar {
  width: 6px;
}
.modal .download-list::-webkit-scrollbar-thumb {
  background-color: #727272;
}
.modal .download-link,
.modal .download-link:hover {
  color: #00a19c !important;
  text-decoration: underline;
}
.modal .download-list .download-item .download-formats {
  display: none;
}
.modal .download-list .download-item .item-wrapper .download-description {
  width: 100%;
}
.modal .download-list .download-item .item-wrapper .download-description a {
  display: flex;
  align-items: center;
}
.modal .download-list .download-item .item-wrapper .download-description a:hover {
  color: #00a19c;
}
.modal .theme-black,
.modal .cta.cta-black a,
.modal .cta.cta-black a p {
  color: #000000;
}
.modal .theme-black,
.modal .cta.cta-black a,
.modal .cta.cta-black a p {
  color: #000000;
}
.modal .cta a p {
  position: relative;
}
.modal .cta a,
.modal .cta a p {
  font-weight: 300;
  font-size: 16px;
  line-height: 24px;
}
.modal .cta a {
  display: flex;
  align-items: center;
}
.modal .cta {
  border-radius: 25px;
  border: 1px solid #e5e5e5;
  padding: 6px 10px;
  display: inline-block;
  padding-right: 25px;
}
.modal .cta.cta-black:hover {
  background-color: #00aa9e;
}
.modal .cta.cta-black:hover a p {
  color: white;
}
.modal .cta.cta-black:hover a img {
  filter: brightness(0) invert(1);
}
.modal .cta a img {
  display: block;
  width: 35px;
  height: 40px;
  margin-right: 15px;
}
@media screen and (min-width: 992px) {
  .modal .cta a img {
    margin-right: 10px;
    transition: all 0.35s ease;
  }
}
@media screen and (min-width: 768px) {
  .modal .modal-bottom .cta {
    margin-top: 20px;
  }
}
.modal .mb-15 {
  margin-bottom: 15px;
}
.modal .mb-30 {
  margin-bottom: 30px;
}
.modal .mt-15 {
  margin-top: 15px;
}
.modal .breakline {
  display: block;
  position: relative;
  width: 100%;
  height: 60px;
}

body .navbar {
  padding-top: 0;
  padding-bottom: 0;
  background: rgba(0, 0, 0, 0);
  height: 88.47px;
  transition: all ease 0.5s;
  padding-right: 0 !important;
  background-color: #ffffff;
}
body .navbar .navbar-collapse {
  flex-basis: unset;
}
@media screen and (min-width: 1100px) {
  body .navbar .navbar-brand {
    position: fixed;
    left: 20px;
  }
}
body .navbar h1,
body .navbar h2,
body .navbar h3,
body .navbar h4,
body .navbar h5,
body .navbar h6 {
  margin-bottom: 15px;
}
body .navbar p,
body .navbar a,
body .navbar span,
body .navbar li,
body .navbar input,
body .navbar textarea,
body .navbar select,
body .navbar label,
body .navbar div,
body .navbar th,
body .navbar td,
body .navbar dt,
body .navbar dd {
  letter-spacing: 0.05rem;
  font-size: 16px;
  line-height: 24px;
}
body .navbar .inherit,
body .navbar h1 span,
body .navbar h2 span,
body .navbar h3 span,
body .navbar h4 span,
body .navbar h5 span,
body .navbar h6 span,
body .navbar h1 .inherit,
body .navbar h2 .inherit,
body .navbar h3 .inherit,
body .navbar h4 .inherit,
body .navbar h5 .inherit,
body .navbar h6 .inherit {
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  display: inline-block;
}
body .navbar h1 a.inherit:hover,
body .navbar h2 a.inherit:hover,
body .navbar h3 .ainherit:hover,
body .navbar h4 a.inherit:hover,
body .navbar h5 a.inherit:hover,
body .navbar h6 a.inherit:hover,
body .navbar h1 a.inherit:hover span,
body .navbar h2 a.inherit:hover span,
body .navbar h3 .ainherit:hover span,
body .navbar h4 a.inherit:hover span,
body .navbar h5 a.inherit:hover span,
body .navbar h6 a.inherit:hover span {
  color: #000000;
}
body .navbar h1 a.inherit:hover,
body .navbar h2 a.inherit:hover,
body .navbar h3 .ainherit:hover,
body .navbar h4 a.inherit:hover,
body .navbar h5 a.inherit:hover,
body .navbar h6 a.inherit:hover,
body .navbar h1 a.inherit:hover span,
body .navbar h2 a.inherit:hover span,
body .navbar h3 .ainherit:hover span,
body .navbar h4 a.inherit:hover span,
body .navbar h5 a.inherit:hover span,
body .navbar h6 a.inherit:hover span,
body .navbar p a,
body .navbar p a.inherit,
body .navbar p a.inherit {
  text-decoration: underline;
}
body .navbar a,
body .navbar a:focus,
body .navbar a:hover,
body .navbar a:active,
body .navbar a:visited,
body .navbar button,
body .navbar button:focus,
body .navbar button:hover,
body .navbar button:active,
body .navbar button:visited,
body .navbar .btn-close,
body .navbar .btn-close:hover,
body .navbar .btn-close:active,
body .navbar .btn-close:focus,
body .navbar .btn-close:visited,
body .navbar .navbar-toggler,
body .navbar .navbar-toggler:focus,
body .navbar .navbar-toggler:visited,
body .navbar .navbar-toggler:hover,
body .navbar .navbar-toggler:active {
  outline: none;
  text-decoration: none;
  box-shadow: none;
}
body .navbar a,
body .navbar a:hover,
body .navbar a:focus,
body .navbar a:visited,
body .navbar a:active,
body .navbar .nav-link,
body .navbar .nav-link:visited,
body .navbar .nav-link:focus {
  color: var(--navbar-text-color);
  font-weight: 700;
  transition: color ease 1s;
}
body .navbar .nav-link.active {
  color: var(--navbar-theme-color);
}
body .navbar a,
body .navbar .owl-dot,
body .navbar .owl-nav button,
body .navbar .owl-nav button img,
body .navbar .newsletter .form-group .newsletter-email,
body .navbar a::before,
body .navbar a::after,
body .navbar input,
body .navbar textarea,
body .navbar select,
body .navbar option {
  transition: all 0.35s ease;
}
body .navbar .theme-green,
body .navbar .link,
body .navbar a:hover,
body .navbar .nav-link:hover {
  color: #00a19c;
}
body .navbar .page-title {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 0;
  opacity: 0;
  padding: 0;
  margin: 0;
  border: 0;
  clip: rect(0, 0, 0, 0);
}
body .navbar .sticky-top.is-sticky {
  box-shadow: 0 10px 35px -11px rgba(138, 138, 138, 0.4);
}
body .navbar .navbar-brand {
  display: block;
  width: 130px;
  padding-top: 21px;
  padding-bottom: 21px;
}
body .navbar .ml-auto {
  margin-left: auto;
}
body .navbar .mr-auto {
  margin-right: auto;
}
body .navbar .btn-close {
  position: absolute;
  right: 15px;
  top: 30px;
}
body .navbar .header-download-button {
  display: flex;
  align-items: center;
}
body .navbar .header-download-button a {
  display: block;
  width: 55px;
  height: 55px;
}
@media screen and (min-width: 992px) {
  body .navbar .navbar-nav .nav-item:not(:last-child) .nav-link {
    margin-right: 10px;
    white-space: nowrap;
  }
  body .navbar .navbar-nav .nav-item:not(:last-child) .nav-link {
    margin-left: 10px;
    white-space: nowrap;
  }
}
@media screen and (min-width: 1600px) {
  body .navbar .navbar-nav .nav-item:not(:last-child) .nav-link {
    margin-right: 30px;
  }
  body .navbar .navbar-nav .nav-item:not(:last-child) .nav-link {
    margin-left: 30px;
  }
}

@media screen and (min-width: 1600px) {
  .navbar .modal.fade .modal-dialog {
    max-width: 1000px;
  }
  .navbar .modal.fade .modal-dialog .modal-body {
    width: 850px;
    margin: 0 auto;
    padding: 70px 0 110px;
  }
}
@media screen and (min-width: 1200px) {
  .navbar .navbar .navbar-nav .nav-item.highlight {
    padding-left: 1.563rem;
    padding-right: 1.563rem;
  }
}
@media screen and (min-width: 768px) and (max-width: 1199px) {
  .navbar h2 {
    font-size: 40px;
    line-height: 44px;
  }
  .navbar h3 {
    font-size: 34px;
    line-height: 38px;
  }
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .navbar .navbar .navbar-nav .nav-item {
    padding-left: 0.938rem;
    padding-right: 0.938rem;
  }
  .navbar .nav-link {
    font-size: 16px;
  }
}
@media screen and (min-width: 992px) and (max-width: 1599px) {
  .navbar .modal.fade .modal-dialog {
    max-width: 800px;
  }
  .navbar .modal.fade .modal-dialog .modal-body {
    width: 660px;
    margin: 0 auto;
    padding: 50px 0 90px;
  }
}
@media screen and (min-width: 992px) {
  .navbar .navbar .container {
    align-items: stretch;
  }
  .navbar .navbar .navbar-nav,
  .navbar .navbar .navbar-nav .nav-item {
    height: 100%;
  }
  .navbar .navbar .navbar-nav .nav-item {
    display: flex;
    align-items: center;
  }
}
@media screen and (max-width: 991px) {
  .navbar .navbar-collapse,
  .navbar .navbar-collapse .navbar-nav {
    z-index: 99;
    top: 100px;
  }
  .navbar .hamburger {
    padding: 0;
    height: 27px;
    position: absolute;
    right: 140px;
  }
  .navbar .hamburger-inner,
  .navbar .hamburger-inner:after,
  .navbar .hamburger-inner:before {
    width: 27px;
    height: 2px;
  }
  .navbar .hamburger-box {
    width: 27px;
  }
  .navbar .hamburger-inner:before {
    top: -8px;
  }
  .navbar .hamburger-inner:after {
    bottom: -8px;
  }
  .navbar .navbar-nav {
    position: fixed;
    background-color: #ffffff;
    width: 100%;
    height: 100vh;
    left: 0;
    padding-top: 80px;
  }
  .navbar .nav-item {
    padding: 15px 30px;
  }
  .navbar .nav-item.highlight {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding-top: 40px;
    padding-bottom: 40px;
  }
  .navbar .navbar-collapse .navbar-nav {
    transform: translateX(100%);
    transition: all 0.5s ease;
  }
  .navbar .navbar-collapse.show .navbar-nav {
    transform: translateX(0);
  }
  .navbar .navbar-nav::before {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 1px;
    background: #9a9a9a;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
    transition: all 0.5s ease;
  }
  .navbar .navbar-nav.nav-open::before {
    width: calc(100% + 60px);
  }
}
@media screen and (min-width: 768px) and (max-width: 991px) {
  .navbar p,
  .navbar a,
  .navbar span,
  .navbar li,
  .navbar input,
  .navbar textarea,
  .navbar select,
  .navbar label,
  .navbar div,
  .navbar th,
  .navbar td,
  .navbar dt,
  .navbar dd {
    font-size: 14px;
    line-height: 20px;
  }
  .navbar .modal .modal-dialog {
    max-width: 650px;
  }
  .navbar .modal .modal-dialog .modal-body {
    padding: 60px 30px;
  }
  .navbar .modal .modal-body .btn-close {
    right: 25px;
  }
}
@media screen and (min-width: 768px) {
  .navbar .navbar-nav .nav-link {
    padding-top: 0.938rem;
    padding-bottom: 0.938rem;
  }
  .navbar .center {
    text-align: center;
  }
  .navbar .right {
    text-align: right;
  }
  .navbar .modal .btn-close {
    right: -30px;
  }
}
@media screen and (max-width: 767px) {
  .navbar h1 span,
  .navbar h2 span,
  .navbar h3 span,
  .navbar h4 span,
  .navbar h5 span,
  .navbar h6 span {
    display: inline;
  }
  .navbar h2,
  .navbar h3,
  .navbar h4,
  .navbar h5,
  .navbar h6 {
    margin-bottom: 30px;
  }
  .navbar h2 {
    font-size: 32px;
    line-height: 40px;
  }
  .navbar h3 {
    font-size: 32px;
    line-height: 40px;
  }
  .navbar .navbar-brand {
    width: 155px;
  }
  .navbar .modal .modal-body {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .navbar .nopadding {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
@media screen and (max-width: 767px) and (min-width: 401px) and (max-width: 767px) {
  .navbar .row > [class*=col-],
  .navbar .navbar .container,
  .navbar .modal .modal-body {
    padding-left: 30px;
    padding-right: 30px;
  }
}
@media screen and (max-width: 767px) and (max-width: 400px) {
  .navbar .row > [class*=col-],
  .navbar .navbar .container,
  .navbar .modal .modal-body {
    padding-left: 25px;
    padding-right: 25px;
  }
}
@media screen and (max-width: 767px) and (max-width: 767px) {
  .navbar .sticky-sidebar {
    display: none !important;
  }
  .navbar .mobile-height {
    height: 1200px;
  }
  .navbar .nav-bar-mobile {
    padding: 11px;
    margin-top: -15px;
  }
}

.footer {
  background-color: #2a353a;
  color: #ffffff;
}
.footer form {
  margin-bottom: unset;
}
.footer p,
.footer a,
.footer span,
.footer li,
.footer input,
.footer textarea,
.footer select,
.footer label,
.footer div,
.footer th,
.footer td,
.footer dt,
.footer dd {
  letter-spacing: 0.05rem;
  font-size: 16px;
  line-height: 24px;
}
.footer .inherit,
.footer h1 span,
.footer h2 span,
.footer h3 span,
.footer h4 span,
.footer h5 span,
.footer h6 span,
.footer h1 .inherit,
.footer h2 .inherit,
.footer h3 .inherit,
.footer h4 .inherit,
.footer h5 .inherit,
.footer h6 .inherit {
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  display: inline-block;
}
.footer h1 a.inherit:hover,
.footer h2 a.inherit:hover,
.footer h3 .ainherit:hover,
.footer h4 a.inherit:hover,
.footer h5 a.inherit:hover,
.footer h6 a.inherit:hover,
.footer h1 a.inherit:hover span,
.footer h2 a.inherit:hover span,
.footer h3 .ainherit:hover span,
.footer h4 a.inherit:hover span,
.footer h5 a.inherit:hover span,
.footer h6 a.inherit:hover span {
  color: #000000;
}
.footer h1 a.inherit:hover,
.footer h2 a.inherit:hover,
.footer h3 .ainherit:hover,
.footer h4 a.inherit:hover,
.footer h5 a.inherit:hover,
.footer h6 a.inherit:hover,
.footer h1 a.inherit:hover span,
.footer h2 a.inherit:hover span,
.footer h3 a.inherit:hover span,
.footer h4 a.inherit:hover span,
.footer h5 a.inherit:hover span,
.footer h6 a.inherit:hover span,
.footer p a,
.footer p a.inherit,
.footer p a.inherit {
  text-decoration: underline;
}
.footer a,
.footer a:focus,
.footer a:hover,
.footer a:active,
.footer a:visited,
.footer button,
.footer button:focus,
.footer button:hover,
.footer button:active,
.footer button:visited {
  outline: none;
  text-decoration: none;
  box-shadow: none;
}
.footer a,
.footer a:hover,
.footer a:focus,
.footer a:visited,
.footer a:active {
  color: #222222;
}
.footer a,
.footer .newsletter .form-group .newsletter-email,
.footer a::before,
.footer a::after,
.footer input {
  transition: all 0.35s ease;
}
.footer .link,
.footer a:hover {
  color: #00a19c;
}
.footer .font24 {
  font-size: 24px;
  line-height: 29px;
}
.footer .footer-bottom .footer-disclaimers {
  margin-bottom: 10px;
  height: 24px;
  align-items: center;
}
.footer .footer-bottom .footer-disclaimers li a {
  opacity: 0.5;
}
.footer .footer-bottom .footer-disclaimers li span {
  padding: 0 5px;
  margin: 0 5px;
  font-weight: 300;
  letter-spacing: 0;
}
.footer .footer-bottom .footer-disclaimers li a:hover {
  opacity: 1;
}
.footer .footer-bottom .footer-disclaimers li {
  position: relative;
}
.footer .footer-bottom .footer-disclaimers li,
.footer .footer-bottom .footer-disclaimers li a {
  line-height: 21px;
}
.footer .footer-bottom .footer-disclaimers li:not(:first-child)::before {
  content: "";
  display: none;
  position: absolute;
  width: 2px;
  height: 15px;
  top: 50%;
  transform: translate(-50%, -50%);
  left: -12px;
  background-color: #ffffff;
  opacity: 1;
}
.footer .footer-bottom .footer-disclaimers li a {
  text-decoration: underline;
  display: inline-block;
  transform: translateY(-1px);
}
.footer .footer-bottom .footer-disclaimers li a,
.footer .footer-bottom .footer-copyright p {
  color: #ffffff;
  font-size: 14px;
  line-height: 21px;
  font-weight: 300;
  letter-spacing: 0;
}
.footer .footer-top {
  padding-bottom: 70px;
}
.footer .footer-bottom .footer-border-top {
  position: relative;
}
.footer .newsletter .form-group,
.footer .newsletter .form-group .newsletter-email {
  display: block;
  position: relative;
  width: 100%;
}
.footer .newsletter .form-group .newsletter-email {
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.6);
  background: transparent;
  padding: 5px 10px;
  color: #ffffff;
  border-radius: 0;
}
.footer .newsletter .form-group .newsletter-email:hover,
.footer .newsletter .form-group .newsletter-email:focus,
.footer .newsletter .form-group .newsletter-email:visited {
  border: none;
  border-bottom: 1px solid #ffffff;
  outline: none;
}
.footer .newsletter .form-group .newsletter-submit {
  display: block;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translate(0, -50%);
  width: 20px;
  height: 20px;
  color: transparent;
  background-color: transparent;
  border: none;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  background-image: url("./../../assets/images/global/pet-icon-submit-white.svg");
}
.footer .newsletter .form-group .newsletter-submit {
  opacity: 0.6;
}
.footer .newsletter .form-group .newsletter-submit:hover {
  opacity: 1;
}
.footer .social-media-list {
  align-items: center;
}
.footer .social-media-list li a::before,
.footer .social-media-list li a::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}
.footer .social-media-list li a::before,
.footer .social-media-list li a:hover::after {
  opacity: 1;
}
.footer .social-media-list li a::before {
  z-index: 1;
}
.footer .social-media-list li a::after {
  opacity: 0;
  z-index: 2;
}
.footer .social-media-list li a,
.footer .social-media-list li a::before,
.footer .social-media-list li a::after {
  display: block;
}
.footer .social-media-list li a {
  position: relative;
}
.footer .social-media-list li a.facebook::before {
  background-image: url("./../../assets/images/global/pet-social-icon-facebook-white.svg");
}
.footer .social-media-list li a.facebook::after {
  background-image: url("./../../assets/images/global/pet-social-icon-facebook-colour.svg");
}
.footer .social-media-list li a.instagram::before {
  background-image: url("./../../assets/images/global/pet-social-icon-instagram-white.svg");
}
.footer .social-media-list li a.instagram::after {
  background-image: url("./../../assets/images/global/pet-social-icon-instagram-colour.svg");
}
.footer .social-media-list li a.twitter::before {
  background-image: url("./../../assets/images/global/pet-social-icon-twitter-white.svg");
}
.footer .social-media-list li a.twitter::after {
  background-image: url("./../../assets/images/global/pet-social-icon-twitter-colour.svg");
}
.footer .social-media-list li a.youtube::before {
  background-image: url("./../../assets/images/global/pet-social-icon-youtube-white.svg");
}
.footer .social-media-list li a.youtube::after {
  background-image: url("./../../assets/images/global/pet-social-icon-youtube-colour.svg");
}
.footer .social-media-list li a.linkedin::before {
  background-image: url("./../../assets/images/global/pet-social-icon-linkedin-white.svg");
}
.footer .social-media-list li a.linkedin::after {
  background-image: url("./../../assets/images/global/pet-social-icon-linkedin-colour.svg");
}
.footer .social-media-list li a.tiktok::before {
  background-image: url("./../../assets/images/global/pet-circle-icon-white-tiktok.svg");
}
.footer .social-media-list li a.tiktok::after {
  background-image: url("./../../assets/images/global/pet-circle-icon-hover-tiktok.svg");
}
.footer .error {
  display: none;
  position: absolute;
  width: 100%;
  top: 100%;
  color: #ff0000;
  font-size: 16px;
  line-height: 1;
  padding: 10px 10px 5px;
}
.footer .error.success {
  color: #28a745;
}
.footer input.processing {
  background: #979797;
  pointer-events: none;
}
.footer .form-loading {
  display: none;
  position: absolute;
  width: 20px;
  top: 50%;
  right: 30px;
  transform: translate(-50%, -50%);
}

.footer {
  padding-top: 70px;
}
.footer .footer-bottom {
  position: relative;
  padding-top: 40px;
  padding-bottom: 40px;
}
.footer .footer-bottom::before {
  content: "";
  display: block;
  position: absolute;
  width: calc(100% - 50px);
  left: 50%;
  top: 0;
  right: 0;
  border-top: 1px solid #90a4ae;
  transform: translate(-50%, 0);
}
.footer .container .row > [class*=footer-] {
  display: flex;
  flex-wrap: wrap;
}
@media screen and (min-width: 768px) and (max-width: 991px) {
  .footer {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}
@media screen and (max-width: 767px) {
  .footer {
    padding: 50px 0;
    text-align: left;
  }
}
@media screen and (min-width: 768px) and (max-width: 1199px) {
  .footer h2 {
    font-size: 40px;
    line-height: 44px;
  }
  .footer h3 {
    font-size: 34px;
    line-height: 38px;
  }
}
@media screen and (min-width: 992px) {
  .footer .hidden-lg {
    display: none !important;
  }
  .footer .footer-bottom > div.col-lg-5 {
    padding-left: 0;
  }
}
@media screen and (min-width: 768px) and (max-width: 991px) {
  .footer p,
  .footer a,
  .footer span,
  .footer li,
  .footer input,
  .footer textarea,
  .footer select,
  .footer label,
  .footer div,
  .footer th,
  .footer td,
  .footer dt,
  .footer dd {
    font-size: 14px;
    line-height: 20px;
  }
  .footer .font24 {
    font-size: 20px;
    line-height: 28px;
  }
  .footer .hidden-md {
    display: none !important;
  }
}
@media screen and (min-width: 768px) {
  .footer .center {
    text-align: center;
  }
  .footer .right {
    text-align: right;
  }
  .footer .flex-end-md {
    display: flex;
    justify-content: flex-end;
  }
  .footer .social-media-list {
    justify-content: flex-end;
  }
  .footer .social-media-list .social-media-item {
    padding: 20px;
  }
  .footer .social-media-list .social-media-item:last-child {
    padding-right: 0;
  }
  .footer .social-media-list .social-media-item a,
  .footer .social-media-list .social-media-item a::before,
  .footer .social-media-list .social-media-item a::after {
    width: 35px;
    height: 35px;
  }
  .footer .footer-top,
  .footer .footer-bottom {
    align-items: center;
  }
}
@media screen and (max-width: 767px) {
  .footer h1 span,
  .footer h2 span,
  .footer h3 span,
  .footer h4 span,
  .footer h5 span,
  .footer h6 span {
    display: inline;
  }
  .footer .hidden-mb {
    display: none;
  }
  .footer h2,
  .footer h3,
  .footer h4,
  .footer h5,
  .footer h6 {
    margin-bottom: 30px;
  }
  .footer h2 {
    font-size: 32px;
    line-height: 40px;
  }
  .footer h3 {
    font-size: 32px;
    line-height: 40px;
  }
  .footer .social-media-list li a,
  .footer .social-media-list li a::before,
  .footer .social-media-list li a::after {
    width: 34px;
    height: 34px;
  }
  .footer .social-media-list {
    width: 100%;
    justify-content: space-between;
    padding: 50px 0;
  }
  .footer .footer-bottom .footer-disclaimers {
    margin-bottom: 8px;
  }
  .footer .footer-top {
    padding-bottom: 0;
  }
  .footer .footer-bottom .footer-border-top {
    padding-top: 0;
  }
  .footer .footer-bottom .footer-border-top::before {
    display: none;
  }
  .footer .mb-order-1 {
    order: 1;
  }
  .footer .mb-order-2 {
    order: 2;
  }
  .footer .footer-bottom .footer-disclaimers li a,
  .footer .footer-bottom .footer-copyright p {
    line-height: 22px;
  }
  .footer .footer-bottom {
    padding-top: 0;
    padding-bottom: 0;
  }
  .footer div[class*=col-],
  .footer .row > div[class*=col-],
  .footer .footer-bottom > div[class*=col-lg] {
    width: 100%;
  }
  .footer .footer-bottom .social-media-list {
    padding: 30px 0;
    width: 100%;
  }
  .footer .footer-bottom .social-media-list .social-media-item a,
  .footer .footer-bottom .social-media-list .social-media-item a::before,
  .footer .footer-bottom .social-media-list .social-media-item a::after {
    width: 35px;
    height: 35px;
  }
  .footer .footer-bottom::before {
    display: none;
  }
}
@media screen and (max-width: 767px) {
  .footer .footer-copyright {
    margin-top: 30px;
  }
}

.footer .nav {
  width: 100%;
  display: flex;
}
.footer .container .row,
.footer .container-fluid .row {
  margin-left: -25px;
  margin-right: -25px;
}
.footer .container .row.rm-margin,
.footer .container-fluid .row.rm-margin {
  margin-left: 0;
  margin-right: 0;
}
.footer .container .row > *,
.footer .container-fluid .row > * {
  padding-left: 0;
  padding-right: 0;
}

.JgeneralFooterContainer {
  overflow: hidden;
  width: 100%;
  z-index: 1;
  display: block;
  position: relative;
}
.JgeneralFooterContainer p {
  margin-top: 15px;
}
.JgeneralFooterContainer p,
.JgeneralFooterContainer a,
.JgeneralFooterContainer span {
  letter-spacing: 0;
}
.JgeneralFooterContainer .field--name-body p {
  margin-bottom: 1rem;
}
.JgeneralFooterContainer .nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0;
  --bs-nav-link-color: var(--bs-link-color);
  --bs-nav-link-hover-color: var(--bs-link-hover-color);
  --bs-nav-link-disabled-color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.JgeneralFooterContainer .nav-link {
  display: block;
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  font-size: var(--bs-nav-link-font-size);
  font-weight: var(--bs-nav-link-font-weight);
  color: var(--bs-nav-link-color);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
.JgeneralFooterContainer a {
  text-decoration: none;
}
.JgeneralFooterContainer a:hover {
  opacity: 0.5;
}
.JgeneralFooterContainer .JgeneralFooterSlider {
  background: #ccc;
  width: 100%;
  height: 500px;
}
.JgeneralFooterContainer .JgeneralFooter {
  z-index: 2;
  position: relative;
  background: #fff;
  padding-top: 100px;
}
.JgeneralFooterContainer .JgeneralFooter2 {
  padding-top: 22px;
}
.JgeneralFooterContainer .JgeneralFooterPattern {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: -19.8%;
  margin-top: -348px;
}
.JgeneralFooterContainer .JgeneralFooterPattern img:nth-child(1) {
  margin-left: -13%;
  margin-left: -160px;
  position: relative;
}
.JgeneralFooterContainer .JgeneralFooterPattern img:nth-child(2) {
  margin-right: -15.9%;
  margin-right: -287px;
  position: absolute;
  right: 0px;
  bottom: -18%;
}
.JgeneralFooterContainer .JgeneralFooterInner {
  width: calc(100% - 250px);
  margin: 0px 110px;
  position: relative;
  z-index: 1;
  padding: 0px 15px;
}
.JgeneralFooterContainer .JgeneralFooterTopBreadcrumb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.JgeneralFooterContainer .JgeneralFooterTopBreadcrumb a {
  font-size: 14px;
  line-height: 20px;
  font-family: "Museo Sans", sans-serif;
  font-weight: 300;
  color: #000;
  padding-right: 25px;
  position: relative;
}
.JgeneralFooterContainer .JgeneralFooterTopBreadcrumb a:after {
  content: "›";
  font-size: 14px;
  line-height: 20px;
  font-family: "Museo Sans", sans-serif;
  font-weight: 300;
  position: absolute;
  right: 10px;
}
.JgeneralFooterContainer .JgeneralFooterTopBreadcrumb a:last-child {
  margin-right: 0px;
  color: #00a19c;
  font-family: "Museo Sans", sans-serif;
  font-weight: normal;
}
.JgeneralFooterContainer .JgeneralFooterTopBreadcrumb a:last-child:after {
  display: none;
}
.JgeneralFooterContainer .JgeneralFooterTopNavi {
  margin-top: 20px;
  padding-top: 30px;
  border-top: 1px solid rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.JgeneralFooterContainer .JgeneralFooterTopNaviLeft ul {
  display: flex;
  align-items: center;
  margin: 0px;
  padding: 0px;
  list-style-type: none;
}
.JgeneralFooterContainer .JgeneralFooterTopNaviLeft ul li {
  margin-right: 40px;
}
.JgeneralFooterContainer .JgeneralFooterTopNaviLeft ul li:last-child {
  margin-right: 0px;
}
.JgeneralFooterContainer .JgeneralFooterTopNaviLeft ul li a,
.JgeneralFooterContainer .JgeneralFooterTopNaviLeft ul li span {
  font-size: 14px;
  line-height: 20px;
  font-family: "Museo Sans", sans-serif;
  font-weight: 300;
  color: #000;
}
.JgeneralFooterContainer .JgeneralFooterTopNaviLeft ul li span:hover {
  cursor: pointer;
  opacity: 0.5;
}
.JgeneralFooterContainer .JgeneralFooterTopNaviRight ul {
  display: flex;
  align-items: center;
  margin: 0px;
  padding: 0px;
  list-style-type: none;
}
.JgeneralFooterContainer .JgeneralFooterTopNaviRight ul li {
  margin-left: 40px;
}
.JgeneralFooterContainer .JgeneralFooterTopNaviRight ul li:first-child {
  margin-left: 0px;
}
.JgeneralFooterContainer .JgeneralFooterTopNaviRight ul li a {
  font-size: 14px;
  line-height: 20px;
  font-family: "Museo Sans", sans-serif;
  font-weight: 300;
  color: #000;
}
.JgeneralFooterContainer .JgeneralFooterBtm {
  margin-top: 60px;
  display: flex;
  justify-content: space-between;
  padding-bottom: 30px;
}
.JgeneralFooterContainer .JgeneralFooterBtmLeft p {
  font-size: 14px;
  line-height: 20px;
  color: #000;
  font-family: "Museo Sans", sans-serif;
  font-weight: 300;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul {
  display: flex;
  list-style-type: none;
  padding: 0px;
  margin: 0px;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul li {
  margin-left: 30px;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul li:first-child {
  margin-left: 0px;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul li a {
  display: block;
  width: 25px;
  height: 25px;
  background-image: url("./../../assets/images/footer/icon-sprite.png");
  background-repeat: no-repeat;
  background-size: 500px auto;
  text-indent: -999em;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul li a.JindexFooterFB {
  background-position: -10px -10px;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul li a.JindexFooterTT {
  background-position: -10px -45px;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul li a.JindexFooterIN {
  background-position: -10px -80px;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul li a.JindexFooterLI {
  background-position: -10px -115px;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul li a.JindexFooterYT {
  background-position: -10px -150px;
}
.JgeneralFooterContainer .JgeneralFooterBtmRight ul li a.JindexFooterTok {
  background-position: -39px -10px;
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooter {
    margin-top: 0px;
    padding-top: 165px;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooter2 {
    padding-top: 20px;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooter2 .JgeneralFooterPattern {
    display: none;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooter .JgeneralFooterPattern {
    margin-top: -70px;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JindexFooter .JindexFooterPattern,
  .JgeneralFooterContainer .JgeneralFooter .JgeneralFooterPattern {
    margin-top: 0px;
    position: absolute;
    top: 0px;
    left: 0px;
    margin-bottom: 0px;
    background-image: url("./../../assets/images/footer/footerLeftPatternM.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 490px;
    top: 15px;
    left: -80px;
    height: 240px;
    width: 490px;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JindexFooter .JindexFooterPattern img,
  .JgeneralFooterContainer .JgeneralFooter .JgeneralFooterPattern img {
    display: none;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JindexRowFooterM .JindexFooterInner,
  .JgeneralFooterContainer .JgeneralFooterInner {
    width: 100%;
    padding: 0px 33px;
    margin: 0px;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooterTopNaviLeft ul,
  .JgeneralFooterContainer .JgeneralFooterTopNaviRight ul {
    display: block;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooterTopNavi > div {
    width: calc(50% - 6px);
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooterTopNaviLeft ul li {
    margin-right: 0px;
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooterTopNavi > div:nth-child(2) {
    margin-left: 12px;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooterTopNavi > div {
    width: calc(50% - 6px);
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooterTopNaviRight ul li {
    margin-left: 0px;
    margin-bottom: 20px;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooterBtm {
    display: block;
    margin-top: 12px;
  }
}
@media only screen and (max-width: 820px) {
  .JgeneralFooterContainer .JgeneralFooterBtmLeft p br {
    display: none;
  }
}

/*!
 * Hamburgers
 * @description Tasty CSS-animated hamburgers
 * <AUTHOR> Suh @jonsuh
 * @site https://jonsuh.com/hamburgers
 * @link https://github.com/jonsuh/hamburgers
 */
.hamburger {
  font: inherit;
  display: inline-block;
  overflow: visible;
  margin: 0;
  padding: 15px;
  cursor: pointer;
  transition-timing-function: linear;
  transition-duration: 0.15s;
  transition-property: opacity, filter;
  text-transform: none;
  color: inherit;
  border: 0;
  background-color: transparent;
}
@media screen and (max-width: 991px) {
  .hamburger {
    padding: 0;
    height: 27px;
  }
}

.hamburger.is-active:hover,
.hamburger:hover {
  opacity: 0.7;
}

.hamburger.is-active .hamburger-inner,
.hamburger.is-active .hamburger-inner:after,
.hamburger.is-active .hamburger-inner:before {
  background-color: var(--hamburger-line);
}

.hamburger-box {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 24px;
}
@media screen and (max-width: 991px) {
  .hamburger-box {
    width: 27px;
  }
}

.hamburger-inner {
  top: 50%;
  display: block;
  margin-top: -2px;
}
@media screen and (max-width: 991px) {
  .hamburger-inner {
    width: 27px;
    height: 2px;
  }
}

.hamburger-inner,
.hamburger-inner:after,
.hamburger-inner:before {
  position: absolute;
  width: 40px;
  height: 4px;
  transition-timing-function: ease;
  transition-duration: 0.15s;
  transition-property: transform;
  border-radius: 4px;
  background-color: var(--hamburger-line);
}
@media screen and (max-width: 991px) {
  .hamburger-inner,
  .hamburger-inner:after,
  .hamburger-inner:before {
    width: 27px;
    height: 2px;
  }
}

.hamburger-inner:after,
.hamburger-inner:before {
  display: block;
  content: "";
}

.hamburger-inner:before {
  top: -10px;
}
@media screen and (max-width: 991px) {
  .hamburger-inner:before {
    top: -8px;
  }
}

.hamburger-inner:after {
  bottom: -10px;
}
@media screen and (max-width: 991px) {
  .hamburger-inner:after {
    bottom: -8px;
  }
}

.hamburger--3dx .hamburger-box {
  perspective: 80px;
}

.hamburger--3dx .hamburger-inner {
  transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dx .hamburger-inner:after,
.hamburger--3dx .hamburger-inner:before {
  transition: transform 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dx.is-active .hamburger-inner {
  transform: rotateY(180deg);
  background-color: transparent !important;
}

.hamburger--3dx.is-active .hamburger-inner:before {
  transform: translate3d(0, 10px, 0) rotate(45deg);
}

.hamburger--3dx.is-active .hamburger-inner:after {
  transform: translate3d(0, -10px, 0) rotate(-45deg);
}

.hamburger--3dx-r .hamburger-box {
  perspective: 80px;
}

.hamburger--3dx-r .hamburger-inner {
  transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dx-r .hamburger-inner:after,
.hamburger--3dx-r .hamburger-inner:before {
  transition: transform 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dx-r.is-active .hamburger-inner {
  transform: rotateY(-180deg);
  background-color: transparent !important;
}

.hamburger--3dx-r.is-active .hamburger-inner:before {
  transform: translate3d(0, 10px, 0) rotate(45deg);
}

.hamburger--3dx-r.is-active .hamburger-inner:after {
  transform: translate3d(0, -10px, 0) rotate(-45deg);
}

.hamburger--3dy .hamburger-box {
  perspective: 80px;
}

.hamburger--3dy .hamburger-inner {
  transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dy .hamburger-inner:after,
.hamburger--3dy .hamburger-inner:before {
  transition: transform 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dy.is-active .hamburger-inner {
  transform: rotateX(-180deg);
  background-color: transparent !important;
}

.hamburger--3dy.is-active .hamburger-inner:before {
  transform: translate3d(0, 10px, 0) rotate(45deg);
}

.hamburger--3dy.is-active .hamburger-inner:after {
  transform: translate3d(0, -10px, 0) rotate(-45deg);
}

.hamburger--3dy-r .hamburger-box {
  perspective: 80px;
}

.hamburger--3dy-r .hamburger-inner {
  transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dy-r .hamburger-inner:after,
.hamburger--3dy-r .hamburger-inner:before {
  transition: transform 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dy-r.is-active .hamburger-inner {
  transform: rotateX(180deg);
  background-color: transparent !important;
}

.hamburger--3dy-r.is-active .hamburger-inner:before {
  transform: translate3d(0, 10px, 0) rotate(45deg);
}

.hamburger--3dy-r.is-active .hamburger-inner:after {
  transform: translate3d(0, -10px, 0) rotate(-45deg);
}

.hamburger--3dxy .hamburger-box {
  perspective: 80px;
}

.hamburger--3dxy .hamburger-inner {
  transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dxy .hamburger-inner:after,
.hamburger--3dxy .hamburger-inner:before {
  transition: transform 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dxy.is-active .hamburger-inner {
  transform: rotateX(180deg) rotateY(180deg);
  background-color: transparent !important;
}

.hamburger--3dxy.is-active .hamburger-inner:before {
  transform: translate3d(0, 10px, 0) rotate(45deg);
}

.hamburger--3dxy.is-active .hamburger-inner:after {
  transform: translate3d(0, -10px, 0) rotate(-45deg);
}

.hamburger--3dxy-r .hamburger-box {
  perspective: 80px;
}

.hamburger--3dxy-r .hamburger-inner {
  transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), background-color 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dxy-r .hamburger-inner:after,
.hamburger--3dxy-r .hamburger-inner:before {
  transition: transform 0s cubic-bezier(0.645, 0.045, 0.355, 1) 0.1s;
}

.hamburger--3dxy-r.is-active .hamburger-inner {
  transform: rotateX(180deg) rotateY(180deg) rotate(-180deg);
  background-color: transparent !important;
}

.hamburger--3dxy-r.is-active .hamburger-inner:before {
  transform: translate3d(0, 10px, 0) rotate(45deg);
}

.hamburger--3dxy-r.is-active .hamburger-inner:after {
  transform: translate3d(0, -10px, 0) rotate(-45deg);
}

.hamburger--arrow.is-active .hamburger-inner:before {
  transform: translate3d(-8px, 0, 0) rotate(-45deg) scaleX(0.7);
}

.hamburger--arrow.is-active .hamburger-inner:after {
  transform: translate3d(-8px, 0, 0) rotate(45deg) scaleX(0.7);
}

.hamburger--arrow-r.is-active .hamburger-inner:before {
  transform: translate3d(8px, 0, 0) rotate(45deg) scaleX(0.7);
}

.hamburger--arrow-r.is-active .hamburger-inner:after {
  transform: translate3d(8px, 0, 0) rotate(-45deg) scaleX(0.7);
}

.hamburger--arrowalt .hamburger-inner:before {
  transition: top 0.1s ease 0.1s, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hamburger--arrowalt .hamburger-inner:after {
  transition: bottom 0.1s ease 0.1s, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hamburger--arrowalt.is-active .hamburger-inner:before {
  top: 0;
  transition: top 0.1s ease, transform 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22) 0.1s;
  transform: translate3d(-8px, -10px, 0) rotate(-45deg) scaleX(0.7);
}

.hamburger--arrowalt.is-active .hamburger-inner:after {
  bottom: 0;
  transition: bottom 0.1s ease, transform 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22) 0.1s;
  transform: translate3d(-8px, 10px, 0) rotate(45deg) scaleX(0.7);
}

.hamburger--arrowalt-r .hamburger-inner:before {
  transition: top 0.1s ease 0.1s, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hamburger--arrowalt-r .hamburger-inner:after {
  transition: bottom 0.1s ease 0.1s, transform 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.hamburger--arrowalt-r.is-active .hamburger-inner:before {
  top: 0;
  transition: top 0.1s ease, transform 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22) 0.1s;
  transform: translate3d(8px, -10px, 0) rotate(45deg) scaleX(0.7);
}

.hamburger--arrowalt-r.is-active .hamburger-inner:after {
  bottom: 0;
  transition: bottom 0.1s ease, transform 0.1s cubic-bezier(0.895, 0.03, 0.685, 0.22) 0.1s;
  transform: translate3d(8px, 10px, 0) rotate(-45deg) scaleX(0.7);
}

.hamburger--arrowturn.is-active .hamburger-inner {
  transform: rotate(-180deg);
}

.hamburger--arrowturn.is-active .hamburger-inner:before {
  transform: translate3d(8px, 0, 0) rotate(45deg) scaleX(0.7);
}

.hamburger--arrowturn.is-active .hamburger-inner:after {
  transform: translate3d(8px, 0, 0) rotate(-45deg) scaleX(0.7);
}

.hamburger--arrowturn-r.is-active .hamburger-inner {
  transform: rotate(-180deg);
}

.hamburger--arrowturn-r.is-active .hamburger-inner:before {
  transform: translate3d(-8px, 0, 0) rotate(-45deg) scaleX(0.7);
}

.hamburger--arrowturn-r.is-active .hamburger-inner:after {
  transform: translate3d(-8px, 0, 0) rotate(45deg) scaleX(0.7);
}

.hamburger--boring .hamburger-inner,
.hamburger--boring .hamburger-inner:after,
.hamburger--boring .hamburger-inner:before {
  transition-property: none;
}

.hamburger--boring.is-active .hamburger-inner {
  transform: rotate(45deg);
}

.hamburger--boring.is-active .hamburger-inner:before {
  top: 0;
  opacity: 0;
}

.hamburger--boring.is-active .hamburger-inner:after {
  bottom: 0;
  transform: rotate(-90deg);
}

.hamburger--collapse .hamburger-inner {
  top: auto;
  bottom: 0;
  transition-delay: 0.13s;
  transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  transition-duration: 0.13s;
}

.hamburger--collapse .hamburger-inner:after {
  top: -20px;
  transition: top 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1) 0.2s, opacity 0.1s linear;
}

.hamburger--collapse .hamburger-inner:before {
  transition: top 0.12s cubic-bezier(0.33333, 0.66667, 0.66667, 1) 0.2s, transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.hamburger--collapse.is-active .hamburger-inner {
  transition-delay: 0.22s;
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: translate3d(0, -10px, 0) rotate(-45deg);
}

.hamburger--collapse.is-active .hamburger-inner:after {
  top: 0;
  transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), opacity 0.1s linear 0.22s;
  opacity: 0;
}

.hamburger--collapse.is-active .hamburger-inner:before {
  top: 0;
  transition: top 0.1s cubic-bezier(0.33333, 0, 0.66667, 0.33333) 0.16s, transform 0.13s cubic-bezier(0.215, 0.61, 0.355, 1) 0.25s;
  transform: rotate(-90deg);
}

.hamburger--collapse-r .hamburger-inner {
  top: auto;
  bottom: 0;
  transition-delay: 0.13s;
  transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  transition-duration: 0.13s;
}

.hamburger--collapse-r .hamburger-inner:after {
  top: -20px;
  transition: top 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1) 0.2s, opacity 0.1s linear;
}

.hamburger--collapse-r .hamburger-inner:before {
  transition: top 0.12s cubic-bezier(0.33333, 0.66667, 0.66667, 1) 0.2s, transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.hamburger--collapse-r.is-active .hamburger-inner {
  transition-delay: 0.22s;
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: translate3d(0, -10px, 0) rotate(45deg);
}

.hamburger--collapse-r.is-active .hamburger-inner:after {
  top: 0;
  transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), opacity 0.1s linear 0.22s;
  opacity: 0;
}

.hamburger--collapse-r.is-active .hamburger-inner:before {
  top: 0;
  transition: top 0.1s cubic-bezier(0.33333, 0, 0.66667, 0.33333) 0.16s, transform 0.13s cubic-bezier(0.215, 0.61, 0.355, 1) 0.25s;
  transform: rotate(90deg);
}

.hamburger--elastic .hamburger-inner {
  top: 2px;
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transition-duration: 0.275s;
}

.hamburger--elastic .hamburger-inner:before {
  top: 10px;
  transition: opacity 0.125s ease 0.275s;
}

.hamburger--elastic .hamburger-inner:after {
  top: 20px;
  transition: transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.hamburger--elastic.is-active .hamburger-inner {
  transition-delay: 75ms;
  transform: translate3d(0, 10px, 0) rotate(135deg);
}

.hamburger--elastic.is-active .hamburger-inner:before {
  transition-delay: 0s;
  opacity: 0;
}

.hamburger--elastic.is-active .hamburger-inner:after {
  transition-delay: 75ms;
  transform: translate3d(0, -20px, 0) rotate(-270deg);
}

.hamburger--elastic-r .hamburger-inner {
  top: 2px;
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transition-duration: 0.275s;
}

.hamburger--elastic-r .hamburger-inner:before {
  top: 10px;
  transition: opacity 0.125s ease 0.275s;
}

.hamburger--elastic-r .hamburger-inner:after {
  top: 20px;
  transition: transform 0.275s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.hamburger--elastic-r.is-active .hamburger-inner {
  transition-delay: 75ms;
  transform: translate3d(0, 10px, 0) rotate(-135deg);
}

.hamburger--elastic-r.is-active .hamburger-inner:before {
  transition-delay: 0s;
  opacity: 0;
}

.hamburger--elastic-r.is-active .hamburger-inner:after {
  transition-delay: 75ms;
  transform: translate3d(0, -20px, 0) rotate(270deg);
}

.hamburger--emphatic {
  overflow: hidden;
}

.hamburger--emphatic .hamburger-inner {
  transition: background-color 0.125s ease-in 0.175s;
}

.hamburger--emphatic .hamburger-inner:before {
  left: 0;
  transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s linear 0.125s, left 0.125s ease-in 0.175s;
}

.hamburger--emphatic .hamburger-inner:after {
  top: 10px;
  right: 0;
  transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s linear 0.125s, right 0.125s ease-in 0.175s;
}

.hamburger--emphatic.is-active .hamburger-inner {
  transition-delay: 0s;
  transition-timing-function: ease-out;
  background-color: transparent !important;
}

.hamburger--emphatic.is-active .hamburger-inner:before {
  top: -80px;
  left: -80px;
  transition: left 0.125s ease-out, top 0.05s linear 0.125s, transform 0.125s cubic-bezier(0.075, 0.82, 0.165, 1) 0.175s;
  transform: translate3d(80px, 80px, 0) rotate(45deg);
}

.hamburger--emphatic.is-active .hamburger-inner:after {
  top: -80px;
  right: -80px;
  transition: right 0.125s ease-out, top 0.05s linear 0.125s, transform 0.125s cubic-bezier(0.075, 0.82, 0.165, 1) 0.175s;
  transform: translate3d(-80px, 80px, 0) rotate(-45deg);
}

.hamburger--emphatic-r {
  overflow: hidden;
}

.hamburger--emphatic-r .hamburger-inner {
  transition: background-color 0.125s ease-in 0.175s;
}

.hamburger--emphatic-r .hamburger-inner:before {
  left: 0;
  transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s linear 0.125s, left 0.125s ease-in 0.175s;
}

.hamburger--emphatic-r .hamburger-inner:after {
  top: 10px;
  right: 0;
  transition: transform 0.125s cubic-bezier(0.6, 0.04, 0.98, 0.335), top 0.05s linear 0.125s, right 0.125s ease-in 0.175s;
}

.hamburger--emphatic-r.is-active .hamburger-inner {
  transition-delay: 0s;
  transition-timing-function: ease-out;
  background-color: transparent !important;
}

.hamburger--emphatic-r.is-active .hamburger-inner:before {
  top: 80px;
  left: -80px;
  transition: left 0.125s ease-out, top 0.05s linear 0.125s, transform 0.125s cubic-bezier(0.075, 0.82, 0.165, 1) 0.175s;
  transform: translate3d(80px, -80px, 0) rotate(-45deg);
}

.hamburger--emphatic-r.is-active .hamburger-inner:after {
  top: 80px;
  right: -80px;
  transition: right 0.125s ease-out, top 0.05s linear 0.125s, transform 0.125s cubic-bezier(0.075, 0.82, 0.165, 1) 0.175s;
  transform: translate3d(-80px, -80px, 0) rotate(45deg);
}

.hamburger--minus .hamburger-inner:after,
.hamburger--minus .hamburger-inner:before {
  transition: bottom 0.08s ease-out 0s, top 0.08s ease-out 0s, opacity 0s linear;
}

.hamburger--minus.is-active .hamburger-inner:after,
.hamburger--minus.is-active .hamburger-inner:before {
  transition: bottom 0.08s ease-out, top 0.08s ease-out, opacity 0s linear 0.08s;
  opacity: 0;
}

.hamburger--minus.is-active .hamburger-inner:before {
  top: 0;
}

.hamburger--minus.is-active .hamburger-inner:after {
  bottom: 0;
}

.hamburger--slider .hamburger-inner {
  top: 2px;
}

.hamburger--slider .hamburger-inner:before {
  top: 10px;
  transition-timing-function: ease;
  transition-duration: 0.15s;
  transition-property: transform, opacity;
}

.hamburger--slider .hamburger-inner:after {
  top: 20px;
}

.hamburger--slider.is-active .hamburger-inner {
  transform: translate3d(0, 10px, 0) rotate(45deg);
}

.hamburger--slider.is-active .hamburger-inner:before {
  transform: rotate(-45deg) translate3d(-5.71429px, -6px, 0);
  opacity: 0;
}

.hamburger--slider.is-active .hamburger-inner:after {
  transform: translate3d(0, -20px, 0) rotate(-90deg);
}

.hamburger--slider-r .hamburger-inner {
  top: 2px;
}

.hamburger--slider-r .hamburger-inner:before {
  top: 10px;
  transition-timing-function: ease;
  transition-duration: 0.15s;
  transition-property: transform, opacity;
}

.hamburger--slider-r .hamburger-inner:after {
  top: 20px;
}

.hamburger--slider-r.is-active .hamburger-inner {
  transform: translate3d(0, 10px, 0) rotate(-45deg);
}

.hamburger--slider-r.is-active .hamburger-inner:before {
  transform: rotate(45deg) translate3d(5.71429px, -6px, 0);
  opacity: 0;
}

.hamburger--slider-r.is-active .hamburger-inner:after {
  transform: translate3d(0, -20px, 0) rotate(90deg);
}

.hamburger--spin .hamburger-inner {
  transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  transition-duration: 0.22s;
}

.hamburger--spin .hamburger-inner:before {
  transition: top 0.1s ease-in 0.25s, opacity 0.1s ease-in;
}

.hamburger--spin .hamburger-inner:after {
  transition: bottom 0.1s ease-in 0.25s, transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.hamburger--spin.is-active .hamburger-inner {
  transition-delay: 0.12s;
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: rotate(225deg);
}

.hamburger--spin.is-active .hamburger-inner:before {
  top: 0;
  transition: top 0.1s ease-out, opacity 0.1s ease-out 0.12s;
  opacity: 0;
}

.hamburger--spin.is-active .hamburger-inner:after {
  bottom: 0;
  transition: bottom 0.1s ease-out, transform 0.22s cubic-bezier(0.215, 0.61, 0.355, 1) 0.12s;
  transform: rotate(-90deg);
}

.hamburger--spin-r .hamburger-inner {
  transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  transition-duration: 0.22s;
}

.hamburger--spin-r .hamburger-inner:before {
  transition: top 0.1s ease-in 0.25s, opacity 0.1s ease-in;
}

.hamburger--spin-r .hamburger-inner:after {
  transition: bottom 0.1s ease-in 0.25s, transform 0.22s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.hamburger--spin-r.is-active .hamburger-inner {
  transition-delay: 0.12s;
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: rotate(-225deg);
}

.hamburger--spin-r.is-active .hamburger-inner:before {
  top: 0;
  transition: top 0.1s ease-out, opacity 0.1s ease-out 0.12s;
  opacity: 0;
}

.hamburger--spin-r.is-active .hamburger-inner:after {
  bottom: 0;
  transition: bottom 0.1s ease-out, transform 0.22s cubic-bezier(0.215, 0.61, 0.355, 1) 0.12s;
  transform: rotate(90deg);
}

.hamburger--spring .hamburger-inner {
  top: 2px;
  transition: background-color 0s linear 0.13s;
}

.hamburger--spring .hamburger-inner:before {
  top: 10px;
  transition: top 0.1s cubic-bezier(0.33333, 0.66667, 0.66667, 1) 0.2s, transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.hamburger--spring .hamburger-inner:after {
  top: 20px;
  transition: top 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1) 0.2s, transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.hamburger--spring.is-active .hamburger-inner {
  transition-delay: 0.22s;
  background-color: transparent !important;
}

.hamburger--spring.is-active .hamburger-inner:before {
  top: 0;
  transition: top 0.1s cubic-bezier(0.33333, 0, 0.66667, 0.33333) 0.15s, transform 0.13s cubic-bezier(0.215, 0.61, 0.355, 1) 0.22s;
  transform: translate3d(0, 10px, 0) rotate(45deg);
}

.hamburger--spring.is-active .hamburger-inner:after {
  top: 0;
  transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), transform 0.13s cubic-bezier(0.215, 0.61, 0.355, 1) 0.22s;
  transform: translate3d(0, 10px, 0) rotate(-45deg);
}

.hamburger--spring-r .hamburger-inner {
  top: auto;
  bottom: 0;
  transition-delay: 0s;
  transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  transition-duration: 0.13s;
}

.hamburger--spring-r .hamburger-inner:after {
  top: -20px;
  transition: top 0.2s cubic-bezier(0.33333, 0.66667, 0.66667, 1) 0.2s, opacity 0s linear;
}

.hamburger--spring-r .hamburger-inner:before {
  transition: top 0.1s cubic-bezier(0.33333, 0.66667, 0.66667, 1) 0.2s, transform 0.13s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.hamburger--spring-r.is-active .hamburger-inner {
  transition-delay: 0.22s;
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: translate3d(0, -10px, 0) rotate(-45deg);
}

.hamburger--spring-r.is-active .hamburger-inner:after {
  top: 0;
  transition: top 0.2s cubic-bezier(0.33333, 0, 0.66667, 0.33333), opacity 0s linear 0.22s;
  opacity: 0;
}

.hamburger--spring-r.is-active .hamburger-inner:before {
  top: 0;
  transition: top 0.1s cubic-bezier(0.33333, 0, 0.66667, 0.33333) 0.15s, transform 0.13s cubic-bezier(0.215, 0.61, 0.355, 1) 0.22s;
  transform: rotate(90deg);
}

.hamburger--stand .hamburger-inner {
  transition: transform 75ms cubic-bezier(0.55, 0.055, 0.675, 0.19) 0.15s, background-color 0s linear 75ms;
}

.hamburger--stand .hamburger-inner:before {
  transition: top 75ms ease-in 75ms, transform 75ms cubic-bezier(0.55, 0.055, 0.675, 0.19) 0s;
}

.hamburger--stand .hamburger-inner:after {
  transition: bottom 75ms ease-in 75ms, transform 75ms cubic-bezier(0.55, 0.055, 0.675, 0.19) 0s;
}

.hamburger--stand.is-active .hamburger-inner {
  transition: transform 75ms cubic-bezier(0.215, 0.61, 0.355, 1) 0s, background-color 0s linear 0.15s;
  transform: rotate(90deg);
  background-color: transparent !important;
}

.hamburger--stand.is-active .hamburger-inner:before {
  top: 0;
  transition: top 75ms ease-out 0.1s, transform 75ms cubic-bezier(0.215, 0.61, 0.355, 1) 0.15s;
  transform: rotate(-45deg);
}

.hamburger--stand.is-active .hamburger-inner:after {
  bottom: 0;
  transition: bottom 75ms ease-out 0.1s, transform 75ms cubic-bezier(0.215, 0.61, 0.355, 1) 0.15s;
  transform: rotate(45deg);
}

.hamburger--stand-r .hamburger-inner {
  transition: transform 75ms cubic-bezier(0.55, 0.055, 0.675, 0.19) 0.15s, background-color 0s linear 75ms;
}

.hamburger--stand-r .hamburger-inner:before {
  transition: top 75ms ease-in 75ms, transform 75ms cubic-bezier(0.55, 0.055, 0.675, 0.19) 0s;
}

.hamburger--stand-r .hamburger-inner:after {
  transition: bottom 75ms ease-in 75ms, transform 75ms cubic-bezier(0.55, 0.055, 0.675, 0.19) 0s;
}

.hamburger--stand-r.is-active .hamburger-inner {
  transition: transform 75ms cubic-bezier(0.215, 0.61, 0.355, 1) 0s, background-color 0s linear 0.15s;
  transform: rotate(-90deg);
  background-color: transparent !important;
}

.hamburger--stand-r.is-active .hamburger-inner:before {
  top: 0;
  transition: top 75ms ease-out 0.1s, transform 75ms cubic-bezier(0.215, 0.61, 0.355, 1) 0.15s;
  transform: rotate(-45deg);
}

.hamburger--stand-r.is-active .hamburger-inner:after {
  bottom: 0;
  transition: bottom 75ms ease-out 0.1s, transform 75ms cubic-bezier(0.215, 0.61, 0.355, 1) 0.15s;
  transform: rotate(45deg);
}

.hamburger--squeeze .hamburger-inner {
  transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  transition-duration: 75ms;
}

.hamburger--squeeze .hamburger-inner:before {
  transition: top 75ms ease 0.12s, opacity 75ms ease;
}

.hamburger--squeeze .hamburger-inner:after {
  transition: bottom 75ms ease 0.12s, transform 75ms cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.hamburger--squeeze.is-active .hamburger-inner {
  transition-delay: 0.12s;
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: rotate(45deg);
}

.hamburger--squeeze.is-active .hamburger-inner:before {
  top: 0;
  transition: top 75ms ease, opacity 75ms ease 0.12s;
  opacity: 0;
}

.hamburger--squeeze.is-active .hamburger-inner:after {
  bottom: 0;
  transition: bottom 75ms ease, transform 75ms cubic-bezier(0.215, 0.61, 0.355, 1) 0.12s;
  transform: rotate(-90deg);
}

.hamburger--vortex .hamburger-inner {
  transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  transition-duration: 0.2s;
}

.hamburger--vortex .hamburger-inner:after,
.hamburger--vortex .hamburger-inner:before {
  transition-delay: 0.1s;
  transition-timing-function: linear;
  transition-duration: 0s;
}

.hamburger--vortex .hamburger-inner:before {
  transition-property: top, opacity;
}

.hamburger--vortex .hamburger-inner:after {
  transition-property: bottom, transform;
}

.hamburger--vortex.is-active .hamburger-inner {
  transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  transform: rotate(765deg);
}

.hamburger--vortex.is-active .hamburger-inner:after,
.hamburger--vortex.is-active .hamburger-inner:before {
  transition-delay: 0s;
}

.hamburger--vortex.is-active .hamburger-inner:before {
  top: 0;
  opacity: 0;
}

.hamburger--vortex.is-active .hamburger-inner:after {
  bottom: 0;
  transform: rotate(90deg);
}

.hamburger--vortex-r .hamburger-inner {
  transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  transition-duration: 0.2s;
}

.hamburger--vortex-r .hamburger-inner:after,
.hamburger--vortex-r .hamburger-inner:before {
  transition-delay: 0.1s;
  transition-timing-function: linear;
  transition-duration: 0s;
}

.hamburger--vortex-r .hamburger-inner:before {
  transition-property: top, opacity;
}

.hamburger--vortex-r .hamburger-inner:after {
  transition-property: bottom, transform;
}

.hamburger--vortex-r.is-active .hamburger-inner {
  transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  transform: rotate(-765deg);
}

.hamburger--vortex-r.is-active .hamburger-inner:after,
.hamburger--vortex-r.is-active .hamburger-inner:before {
  transition-delay: 0s;
}

.hamburger--vortex-r.is-active .hamburger-inner:before {
  top: 0;
  opacity: 0;
}

.hamburger--vortex-r.is-active .hamburger-inner:after {
  bottom: 0;
  transform: rotate(-90deg);
}

.sticky-nav-bar {
  display: none;
  position: fixed;
  z-index: 3;
  background-color: var(--background-secondary-color);
  right: 0;
  transform: translateX(100px) translateY(-50%);
  border-radius: 20px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  transition: all 0.35s ease;
  transition-delay: 500ms;
  top: 50%;
}
.sticky-nav-bar.complete {
  transform: translateX(0px) translateY(-50%);
}
@media (min-width: 576px) {
  .sticky-nav-bar {
    display: block;
  }
}
.sticky-nav-bar .nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.sticky-nav-bar .sticky-sidebar {
  display: flex;
  flex-direction: column;
}
.sticky-nav-bar .sticky-sidebar .nav-item:not(:last-child) {
  border-bottom: 1px solid var(--border-white-color);
}
.sticky-nav-bar .sticky-sidebar .nav-item {
  padding: 18px;
  position: relative;
}
.sticky-nav-bar .sticky-nav-icon {
  width: 30px;
}

section.download-section {
  background-color: var(--background-theme-color);
  padding: 15px 0px;
}
section.download-section .activity-download-wrapper {
  background-image: url("./../../assets/images/global/download-background.png");
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 15px;
}
@media (max-width: 575px) {
  section.download-section .activity-download-wrapper {
    min-height: 130px;
  }
}
section.download-section .activity-download-wrapper .activity-download {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 15px 10px;
}
@media (min-width: 992px) {
  section.download-section .activity-download-wrapper .activity-download {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 180px;
    max-width: 530px;
    margin: 0 auto;
  }
}
section.download-section .activity-download-wrapper .activity-download h5 {
  max-width: 305px;
}
@media (max-width: 992px) {
  section.download-section .activity-download-wrapper .activity-download h5 {
    font-size: 14px;
    line-height: 18px;
    height: unset;
  }
}
@media (max-width: 575px) {
  section.download-section .activity-download-wrapper .activity-download h5 {
    height: 55px;
  }
}
section.download-section .activity-download-wrapper .activity-download .btn-download-activity {
  padding: 10px;
  padding-left: 45px;
  border-radius: 30px;
  background-image: url("./../../assets/images/global/green-button.png");
  background-repeat: no-repeat;
  background-size: 30px 30px;
  color: var(--text-white-color);
  font-weight: 300;
  height: 31px;
  margin-top: 10px;
}
section.download-section .activity-download-wrapper .activity-download .btn-download-activity p {
  display: none;
  white-space: nowrap;
}
@media (min-width: 992px) {
  section.download-section .activity-download-wrapper .activity-download .btn-download-activity {
    background: var(--background-theme-color);
    background-repeat: no-repeat;
    background-size: 30px 30px;
    background-image: url("./../../assets/images/global/button.png");
    padding-left: 45px;
    height: unset;
    background-position-x: 10px;
    background-position-y: 5px;
    margin-top: 0px;
  }
  section.download-section .activity-download-wrapper .activity-download .btn-download-activity p {
    display: block;
  }
}

#home section.section {
  min-height: var(--doc-height);
  position: relative;
}
#home .responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
#home .main {
  padding: 0px 0px 100px 0px;
  display: flex;
  height: 100%;
  align-items: flex-end;
}
#home .pin-section {
  height: var(--doc-height);
  padding: 0px 0px 100px 0px;
  touch-action: none;
  pointer-events: none;
}
@media (max-width: 639px) {
  #home .pin-section {
    padding: 0px 0px 80px 0px;
  }
}
@media (max-width: 639px) {
  #home .section-video-title {
    margin-bottom: 60px;
  }
}
#home .section-video-title h4 {
  font-family: "Museo Sans";
  font-weight: 700;
  color: var(--text-black-color);
}
#home .section-video-title h4 span {
  color: var(--text-theme-color);
}
#home .video-content {
  position: absolute;
  overflow: hidden;
  border-radius: 10px;
  clip-path: inset(100% round 20px);
  transition: all ease 1s;
}
#home .video-content.animated {
  clip-path: inset(0% round 20px);
}
@media (min-width: 640px) {
  #home .video-content {
    border-radius: 30px;
  }
}
#home .video-content::after {
  content: "";
  height: 338px;
  width: 100%;
  position: absolute;
  bottom: 0px;
  z-index: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgb(84, 84, 84));
  display: block;
}
#home .video-content iframe {
  width: 100%;
  height: 100%;
  pointer-events: none;
}
#home .video-content .video-content-desc {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  padding: 10px;
  display: flex;
  flex-flow: column;
  align-items: flex-start;
  gap: 30px;
  z-index: 1;
  --p-font-size: 16px;
  --p-line-height: 20px;
}
#home .video-content .video-content-desc h4 {
  font-weight: 700;
}
#home .video-content .video-content-desc p {
  font-weight: 300;
}
@media (min-width: 620px) {
  #home .video-content .video-content-desc {
    padding: 80px;
  }
}
#home .video-d.hide-desktop {
  margin-top: 10px;
  --text-white-color: var(--text-black-color);
}
@media (min-width: 640px) {
  #home .video-d.hide-desktop {
    display: none;
  }
}
@media (max-width: 639px) {
  #home .video-d.hide-mobile {
    display: none;
  }
}
#home .video-d h4 {
  color: var(--text-white-color);
}
#home .video-d h4 + p {
  margin-top: 10px;
}
#home .video-d p {
  color: var(--text-white-color);
  max-width: 1000px;
}
#home #together-for-our-future {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: flex-end;
  padding-bottom: 4rem;
}
#home #together-for-our-future .together-desktop {
  display: none;
}
@media (min-width: 640px) {
  #home #together-for-our-future .together-desktop {
    display: block;
  }
}
#home #together-for-our-future .together-mobile {
  /* position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 90%;
  z-index: -1; */
  position: absolute;
  left: 0px;
  /* top: 10%; */
  top: 28%;
  width: 100%;
  height: 70%;
  z-index: -1;
}

/* #home #together-for-our-future .together-mobile video {
  position: absolute;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
  top: 3px;
  height: 90%;
  transform: translate(-50%, -50%) scale(0.5);
} */

@media screen and (max-width:430px) {
  #home #together-for-our-future .together-mobile .video-background-mobile {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    transform: scale(1.02);
  }

  /* #home #together-for-our-future .together-mobile .video-background-mobile video {
    position: absolute;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
    top: 3px;
    height: 90%;
    transform: translate(-50%, -50%) scale(0.5);
  } */
  #home #together-for-our-future .together-mobile .video-background-mobile video {
    position: absolute;
    -o-object-fit: cover;
    object-fit: contain;
    width: 100%;
    top: 3px;
    height: 90%;
    transform: translate(0%, 0%) scale(2);
  }
}
@media (min-width: 640px) {
  #home #together-for-our-future .together-mobile {
    display: none;
  }
}
#home #together-for-our-future .section-title {
  display: flex;
  flex-flow: column;
  top: 7rem;
  position: absolute;
}
#home #together-for-our-future .section-title p {
  font-family: "Museo Sans";
  /* color: var(--text-black-color); */
  color: #00a19c;
  /* font-weight: 500; */
  font-weight: 900;
}
#home #together-for-our-future .section-title h1 {
  font-family: "Museo Sans";
  /* color: var(--text-black-color); */
  color: #00a19c;
  font-weight: 900;
  letter-spacing: -7px;
  margin-left: -5px;
  margin-top: -10px;
}
@media (max-width: 639px) {
  #home #together-for-our-future .section-title h1 {
    letter-spacing: 0;
  }
}

#home #together-for-our-future .section-title h2 {
  font-family: "Museo Sans";
  /* color: var(--text-black-color); */
  color: #00a19c;
  font-weight: 900;
  letter-spacing: -7px;
  margin-left: -5px;
  margin-top: -10px;
}
@media (max-width: 639px) {
  #home #together-for-our-future .section-title h2 {
    letter-spacing: 0;
  }
}
#home #together-for-our-future .section-title h6 {
  font-family: "Museo Sans";
  /* color: var(--text-black-color); */
  color: #00a19c;
  font-weight: 700;
  margin-top: -10px;
}
#home #together-for-our-future .video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  transform: scale(1.02);
}
@media (min-width: 640px) {
  #home #together-for-our-future .video-background {
    transform: scale(1);
    /* transform: scale(0.98); */
  }
}
@media (min-width: 1440px) {
  #home #together-for-our-future .video-background {
    transform: scale(1.02);
    /* transform: scale(0.9); */
  }
}
#home #together-for-our-future .video-background video {
  position: absolute;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  /* height: auto; */
  height: 100vh;
  -o-object-fit: cover;
     object-fit: cover;
  top: 50%;
  left: 222%;
  transform: translate(-100%, -50%) scale(0.5);
}
@media (min-width: 640px) {
  #home #together-for-our-future .video-background video {
    -o-object-fit: contain;
       object-fit: contain;
    top: 50%;
    left: 50%;
    /* transform: translate(-50%, -50%) scale(0.6); */
    transform: translate(-50%, -50%);
    /* min-width: 90%;
    width: 90%; */
  }
}
@media (min-width: 1440px) {
  #home #together-for-our-future .video-background video {
    top: 50%;
    left: 50%;
    /* transform: translate(-50%, -50%) scale(0.8); */
    transform: translate(-50%, -50%);
    height: 100vh;
    -o-object-fit: contain;
       object-fit: contain;
  }
}
@media (min-width: 1660px) {
  #home #together-for-our-future .video-background video {
    /* transform: translate(-50%, -50%) scale(1); */
    transform: translate(-50%, -50%);
    /* -o-object-fit: cover;
       object-fit: cover; */
    -o-object-fit: contain;
    object-fit: contain;
  }
}
#home #our-purpose {
  background-image: url("./../../assets/images/our-purpose/background.png");
  background-repeat: no-repeat;
  background-size: 90% auto;
  position: relative;
  z-index: 2;
  margin-top: -1px;
}
@media (max-width: 639px) {
  #home #our-purpose {
    background-size: 70% auto!important;
    background-position: 0% 3%;
  }
}
#home #our-purpose .aim-carousel-mobile {
  display: block;
}
@media (min-width: 640px) {
  #home #our-purpose .aim-carousel-mobile {
    display: none;
  }
}
#home #our-purpose .aim-carousel-desktop {
  display: none;
}
@media (min-width: 640px) {
  #home #our-purpose .aim-carousel-desktop {
    display: block;
  }
}
#home #our-purpose .aim-carousel-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  margin-top: 40px;
}
@media (min-width: 640px) {
  #home #our-purpose .aim-carousel-wrapper {
    margin-top: 0px;
  }
}
#home #our-purpose .aim-carousel-wrapper .aim-carousel-item {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  transform: scale(0.9) translateY(-50px);
  transition: transform ease 500ms;
  top: 0px;
  left: 0px;
}
#home #our-purpose .aim-carousel-wrapper .aim-carousel-item img {
  border-radius: 30px;
  overflow: hidden;
}
@media (min-width: 640px) {
  #home #our-purpose .aim-carousel-wrapper .aim-carousel-item {
    border-radius: 30px;
  }
}
#home #our-purpose .aim-carousel-wrapper .aim-carousel-item.active {
  position: relative;
  transform: scale(1) translateY(0px);
  z-index: 1;
}
#home #our-purpose .nav-pills {
  border-radius: 10px;
  border: 1px solid #000000;
  display: inline-flex;
  flex-direction: row;
  width: 100%;
  max-width: 500px;
}
@media (min-width: 1440px) {
  #home #our-purpose .nav-pills {
    border-radius: 30px;
  }
}
#home #our-purpose .nav-pills .nav-item {
  border-radius: 10px;
  overflow: hidden;
  flex: 1;
}
@media (min-width: 1440px) {
  #home #our-purpose .nav-pills .nav-item {
    border-radius: 30px;
  }
}
#home #our-purpose .nav-pills .nav-link {
  padding: 14px 0px;
  text-align: center;
  font-family: "Museo Sans";
  font-weight: 500;
  color: var(--text-black-color);
  width: 100%;
  font-size: 20px;
  line-height: 24px;
}
#home #our-purpose .nav-pills .nav-link.active {
  background: var(--background-light-theme-color);
  color: var(--text-white-color);
}
#home #our-purpose .our-purpose-aim {
  padding: 50px 0px;
}
@media (min-width: 640px) {
  #home #our-purpose .our-purpose-aim {
    padding: 100px 0px;
  }
}
#home #our-purpose .our-purpose-wrapper {
  padding: 50px 0px;
}
@media (min-width: 640px) {
  #home #our-purpose .our-purpose-wrapper {
    padding: 150px 0px;
  }
}
#home #our-purpose .our-purpose-wrapper .section-title {
  transition: all ease 200ms;
}
@media (max-width: 639px) {
  #home #our-purpose .our-purpose-wrapper .section-title {
    margin-bottom: 60px;
  }
}
#home #our-purpose .our-purpose-wrapper .section-title h1 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
#home #our-purpose .our-purpose-wrapper .section-title h1 span {
  color: var(--text-theme-color);
}
#home #our-purpose .our-purpose-wrapper .section-title h2 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
#home #our-purpose .our-purpose-wrapper .section-title h2 span {
  color: var(--text-theme-color);
}
#home #our-purpose .our-purpose-wrapper h2 {
  font-family: "Museo Sans";
  font-weight: 500;
  color: var(--text-black-color);
}
/* @media (max-width: 1399px) {
  #home #our-purpose .our-purpose-wrapper h2 {
    font-size: var(--p-font-size);
    line-height: var(--p-line-height);
  }
} */
@media (min-width: 640px) {
  #home #our-purpose .our-purpose-video {
    padding: 100px 0px 50px 0px;
  }
}
#home #our-purpose .swiper-aim-wrapper {
  height: 80vh;
  padding: 50px 0px 100px 0px;
}
#home #our-purpose .aim-img {
  border-radius: 20px;
  overflow: hidden;
}
#home #our-purpose .aim-content-wrapper {
  min-height: 400px;
  height: 100%;
  max-height: 400px;
  display: inline-flex;
  flex-flow: column;
  align-items: flex-start;
  gap: 20px;
  padding-top: 50px;
}
@media (max-width: 639px) {
  #home #our-purpose .aim-content-wrapper {
    max-height: unset;
  }
}
#home #our-purpose .aim-content-wrapper .aim-content {
  display: flex;
  flex-flow: column;
  gap: 20px;
  justify-content: center;
  max-width: 580px;
  --p-font-size: 20px;
  --p-line-height: 24px;
}
#home #our-purpose .aim-content-wrapper .aim-content h4 {
  font-family: "Museo Sans";
  font-weight: 700;
  color: var(--text-black-color);
}
#home #our-purpose .aim-content-wrapper .aim-content h4 span {
  color: var(--text-theme-color);
}
#home #our-purpose .aim-content-wrapper .aim-content h5 {
  font-family: "Museo Sans";
  font-weight: 700;
  color: var(--text-black-color);
}
#home #our-purpose .aim-content-wrapper .aim-content h5 span {
  color: var(--text-theme-color);
}
#home #our-purpose .aim-content-wrapper .aim-content p {
  font-family: "Museo Sans";
  font-weight: 500;
  color: var(--text-black-color);
}
#home #financial-highlights {
  position: relative;
  padding: 50px 0px;
  min-height: unset;
  z-index: 1;
  --progress-height: 550px;
}
@media (min-width: 640px) {
  #home #financial-highlights {
    padding: 100px 0px;
  }
}
#home #financial-highlights .vector-img {
  /* max-width: 550px; */
  max-width: 400px;
  width: 100%;
  position: absolute;
  top: -150px;
  /* right: 0px; */
  right: 80px;
}
@media (max-width: 639px) {
  #home #financial-highlights .vector-img {
    max-width: 300px;
    top: 0px;
    right: 0px;
    z-index: -1;
  }
}
#home #financial-highlights .chart-box-wrapper {
  display: flex;
  flex-flow: column;
  width: 100%;
}
@media (min-width: 640px) {
  #home #financial-highlights .chart-box-wrapper {
    flex-flow: row-reverse;
    align-items: flex-end;
    gap: 40px;
  }
}
#home #financial-highlights .chart-box-wrapper .progress-content-wrapper {
  max-width: 400px;
  width: 100%;
}
@media only screen and (min-device-width: 320px) and (max-device-width: 640px) and (min-device-height: 568px) and (max-device-height: 1136px) and (-webkit-device-pixel-ratio: 2) {
  #home #financial-highlights {
    --progress-height: 450px;
  }
}
#home #financial-highlights .financial-progress-wrapper {
  align-items: flex-end;
  display: flex;
  gap: 20px;
  flex-direction: row;
  height: var(--progress-height);
}
@media (min-width: 640px) {
  #home #financial-highlights .financial-progress-wrapper {
    gap: 100px;
  }
}
#home #financial-highlights .financial-progress-wrapper .financial-content {
  max-width: 300px;
  display: flex;
  flex-flow: column;
  gap: 20px;
}
@media (max-width: 639px) {
  #home #financial-highlights .financial-progress-wrapper .financial-content {
    max-width: unset;
  }
}
#home #financial-highlights .financial-progress-wrapper .financial-content h5 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
@media (max-width: 639px) {
  #home #financial-highlights .financial-progress-wrapper .financial-content h5 {
    font-size: var(--h6-font-size);
    line-height: var(--h6-line-height);
  }
}
#home #financial-highlights .financial-progress-wrapper .financial-content p {
  font-family: "Museo Sans";
  color: var(--text-black-color);
}
#home #financial-highlights .progress-bar-wrapper {
  height: var(--progress-height);
}
#home #financial-highlights .ratio {
  --bs-aspect-ratio: 70%;
}
#home #financial-highlights .ratio .number-display {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  width: 100%;
  position: absolute;
}
@media (min-width: 640px) {
  #home #financial-highlights .ratio .number-display {
    top: 70%;
  }
}
#home #financial-highlights .ratio .number-display p {
  font-family: "Museo Sans";
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
  color: var(--text-black-color);
  position: relative;
}
@media (min-width: 640px) {
  #home #financial-highlights .ratio .number-display p {
    font-size: 30px;
    line-height: 34px;
  }
}
#home #financial-highlights .ratio .number-display p:first-child {
  left: -20px;
}
@media (min-width: 640px) {
  #home #financial-highlights .ratio .number-display p:first-child {
    left: -50px;
  }
}
#home #financial-highlights .ratio .number-display p:last-child {
  left: 40px;
}
@media (min-width: 640px) {
  #home #financial-highlights .ratio .number-display p:last-child {
    left: 100px;
  }
}
#home #financial-highlights .ratio .year-display {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 20px;
  position: absolute;
  left: 50%;
  bottom: 10px;
  transform: translateX(-50%);
}
@media (min-width: 640px) {
  #home #financial-highlights .ratio .year-display {
    bottom: 0px;
    gap: 100px;
  }
}
@media (min-width: 1600px) {
  #home #financial-highlights .ratio .year-display {
    gap: 190px;
  }
}
#home #financial-highlights .ratio .year-display span {
  font-family: "Museo Sans";
  font-size: 15px;
  line-height: 19px;
  font-weight: 700;
  color: var(--text-black-color);
}
@media (min-width: 640px) {
  #home #financial-highlights .ratio .year-display span {
    font-size: 28px;
    line-height: 32px;
  }
}
#home #financial-highlights .ratio canvas {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: bottom center;
     object-position: bottom center;
}
@media (min-width: 640px) {
  #home #financial-highlights .ratio canvas {
    -o-object-fit: cover;
       object-fit: cover;
  }
}
@media (max-width: 639px) {
  #home #financial-highlights .ratio {
    --bs-aspect-ratio: 100%;
  }
}
@media (max-width: 639px) {
  #home #financial-highlights .financial-box-chart-wrapper {
    display: none;
  }
}
#home #financial-highlights .financial-box-chart-wrapper .financial-2022-wrapper,
#home #financial-highlights .financial-box-chart-wrapper .financial-2023-wrapper {
  position: absolute;
}
#home #financial-highlights .financial-box-chart-wrapper .financial-2022-wrapper p,
#home #financial-highlights .financial-box-chart-wrapper .financial-2023-wrapper p {
  font-family: "Museo Sans";
  --p-font-size: 40px;
  --p-line-height: 44px;
  font-weight: 700;
  letter-spacing: -1;
  color: var(--text-black-color);
}
#home #financial-highlights .financial-box-chart-wrapper .financial-2022-wrapper p span,
#home #financial-highlights .financial-box-chart-wrapper .financial-2023-wrapper p span {
  letter-spacing: 0px;
}
#home #financial-highlights .financial-box-chart-wrapper .financial-2022-wrapper {
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
}
#home #financial-highlights .financial-box-chart-wrapper .financial-2023-wrapper {
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
#home #financial-highlights .section-title {
  transition: all ease 500ms;
  padding-bottom: 10px;
  text-align: center;
}
#home #financial-highlights .section-title h2 {
  color: var(--text-black-color);
  font-family: "Museo Sans";
  font-weight: 900;
}
@media (max-width: 639px) {
  #home #financial-highlights .section-title h2 {
    --h1-font-size: 55px;
    --h1-line-height: 59px;
  }
}
#home #financial-highlights .section-title h2 span {
  color: var(--text-theme-color);
}
#home #financial-highlights .section-title h3 {
  color: var(--text-black-color);
  font-family: "Museo Sans";
  font-weight: 900;
}
@media (max-width: 639px) {
  #home #financial-highlights .section-title h3 {
    --h1-font-size: 55px;
    --h1-line-height: 59px;
  }
}
#home #financial-highlights .section-title h3 span {
  color: var(--text-theme-color);
}
#home #financial-highlights .section-content {
  margin: 0 auto;
  /* margin-top: 50px; */
  text-align: center;
  display: flex;
  flex-flow: column;
  gap: 60px;
}
@media (max-width: 639px) {
  #home #financial-highlights .section-content {
    margin-top: unset;
  }
}
#home #financial-highlights .section-content h5 {
  font-family: "Museo Sans";
  font-weight: 700;
  --h5-font-size: 35px;
  --h5-line-height: 39px;
}
#home #financial-highlights .section-content h5 span {
  color: var(--text-theme-color);
}
#home #financial-highlights .section-content p {
  font-family: "Museo Sans";
  max-width: 816px;
  margin: 0 auto;
}
#home #key-messages .section-content {
  text-align: center;
}
#home #sustainability-is-embedded-across-our-business .section-content {
  text-align: center;
  margin-bottom: 50px;
}
#home #progress-animation-3d-bar {
  position: relative;
  padding: 50px 0px;
  /* --progress-height: 550px; */
  --progress-height: 70vh;
  /* height: 80vh; */
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar {
    padding: 100px 0px 0px 0px;
  }
}
#home #progress-animation-3d-bar .chart-box-wrapper {
  display: flex;
  flex-flow: column;
  width: 100%;
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .chart-box-wrapper {
    flex-flow: row-reverse;
    align-items: flex-end;
    gap: 40px;
  }
}
#home #progress-animation-3d-bar .chart-box-wrapper .progress-content-wrapper {
  max-width: 400px;
  width: 100%;
}
@media only screen and (min-device-width: 320px) and (max-device-width: 640px) and (min-device-height: 568px) and (max-device-height: 1136px) and (-webkit-device-pixel-ratio: 2) {
  #home #progress-animation-3d-bar {
    --progress-height: 450px;
  }
}
#home #progress-animation-3d-bar .financial-progress-wrapper {
  align-items: flex-end;
  display: flex;
  gap: 20px;
  flex-direction: row;
  /* height: var(--progress-height); */
  height: 100vh;
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .financial-progress-wrapper {
    gap: 100px;
  }
}
#home #progress-animation-3d-bar .financial-progress-wrapper .financial-content {
  max-width: 300px;
  display: flex;
  flex-flow: column;
  gap: 20px;
}
@media (max-width: 639px) {
  #home #progress-animation-3d-bar .financial-progress-wrapper .financial-content {
    max-width: unset;
  }
}
#home #progress-animation-3d-bar .financial-progress-wrapper .financial-content h5 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
@media (max-width: 639px) {
  #home #progress-animation-3d-bar .financial-progress-wrapper .financial-content h5 {
    font-size: 25px;
    line-height: 30px;
  }
}
#home #progress-animation-3d-bar .financial-progress-wrapper .financial-content h6 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
@media (max-width: 639px) {
  #home #progress-animation-3d-bar .financial-progress-wrapper .financial-content h6 {
    font-size: 25px;
    line-height: 30px;
  }
}
#home #progress-animation-3d-bar .financial-progress-wrapper .financial-content p {
  font-family: "Museo Sans";
  color: var(--text-black-color);
}
#home #progress-animation-3d-bar .progress-bar-wrapper {
  height: var(--progress-height);
}
#home #progress-animation-3d-bar .ratio {
  /* --bs-aspect-ratio: 90%; */
  --bs-aspect-ratio: 70%;
}
/* #home #progress-animation-3d-bar .ratio {
  --bs-aspect-ratio: 90vh;
} */
#home #progress-animation-3d-bar .ratio .number-display {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
  width: 100%;
  position: absolute;
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .ratio .number-display {
    /* top: 70%; */
    top:50%
  }
}
#home #progress-animation-3d-bar .ratio .number-display p {
  font-family: "Museo Sans";
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
  color: var(--text-black-color);
  position: relative;
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .ratio .number-display p {
    font-size: 30px;
    line-height: 34px;
  }
}
#home #progress-animation-3d-bar .ratio .number-display p:first-child {
  left: -10px;
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .ratio .number-display p:first-child {
    /* left: -50px; */
    left: -30px;
  }
}
@media (min-width: 1600px) {
  #home #progress-animation-3d-bar .ratio .number-display p:first-child {
    /* left: -50px; */
    left: -100px;
  }
}
@media (max-width: 639px) {
  #home #progress-animation-3d-bar .ratio .number-display p:last-child {
    left: unset;
    right: -20px;
  }
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .ratio .number-display p:last-child {
    left: -85px;
  }
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .ratio .number-display p:last-child {
    /* left: 100px; */
    left: 70px;
  }
}
@media (min-width: 1400px) {
  #home #progress-animation-3d-bar .ratio .number-display p:last-child {
    left: 80px;
  }
}
@media (min-width: 1600px) {
  #home #progress-animation-3d-bar .ratio .number-display p:last-child {
    /* left: 100px; */
    left: 160px;
  }
}
#home #progress-animation-3d-bar .ratio .year-display {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 20px;
  position: absolute;
  left: 50%;
  bottom: 10px;
  transform: translateX(-50%);
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .ratio .year-display {
    bottom: 0px;
    gap: 100px;
  }
}
@media (min-width: 1600px) {
  #home #progress-animation-3d-bar .ratio .year-display {
    /* gap: 190px; */
    gap: 135px;
  }
}
#home #progress-animation-3d-bar .ratio .year-display span {
  font-family: "Museo Sans";
  font-size: 15px;
  line-height: 19px;
  font-weight: 700;
  color: var(--text-black-color);
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .ratio .year-display span {
    font-size: 28px;
    line-height: 32px;
  }
}
#home #progress-animation-3d-bar .ratio canvas {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: bottom center;
     object-position: bottom center;
}
@media (min-width: 640px) {
  #home #progress-animation-3d-bar .ratio canvas {
    -o-object-fit: cover;
       object-fit: cover;
  }
}
@media (max-width: 639px) {
  #home #progress-animation-3d-bar .ratio {
    --bs-aspect-ratio: 100%;
  }
}
#home #progress-animation-3d-bar .financial-wrapper {
  position: sticky;
  top: 0px;
}
#home #progress-animation-3d-bar .progress-bar-vertical {
  width: 10px;
  min-height: 100%;
  display: flex;
  align-items: flex-end;
  transform: rotate(180deg);
}
#home #progress-animation-3d-bar .progress-bar-vertical .progress-bar {
  width: 100%;
  height: 0;
  transition: height 0.6s ease;
  background-color: var(--text-theme-color);
}
#home #petronas-audited-financial-statements-2023 {
  background-image: url("./../../assets/images/petronas-audited-financial-statements-2023/background.png");
  background-color: #f5f5f5;
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position-x: right;
  background-position-y: center;
  padding: 50px 0px;
  min-height: unset;
  --btn-margin-top: 50px;
}
@media (max-width: 639px) {
  #home #petronas-audited-financial-statements-2023 {
    padding: 50px 0px;
    background: unset;
    background-color: #f5f5f5;
  }
}
#home #petronas-audited-financial-statements-2023 .section-content {
  clip-path: rect(0 0% 100% 0);
  transition: all ease 500ms;
}
#home #petronas-audited-financial-statements-2023 .section-content h3 {
  color: var(--text-black-color);
  font-family: "Museo Sans";
  font-weight: 900;
}
#home #petronas-audited-financial-statements-2023 .section-content h3 span {
  display: block;
  color: var(--text-theme-color);
}
#home #petronas-audited-financial-statements-2023 .section-content h5 {
  color: var(--text-black-color);
  font-family: "Museo Sans";
  font-weight: 900;
}
#home #petronas-audited-financial-statements-2023 .section-content h5 span {
  display: block;
  color: var(--text-theme-color);
}
#home #petronas-audited-financial-statements-2023 .section-content.animated {
  clip-path: rect(0 100% 100% 0);
}
#home #petronas-audited-financial-statements-2023 .audited-financial-img {
  /* max-width: 300px; */
  max-width: 400px;
  margin-top: 20px;
}
@media (max-width: 639px) {
  #home #petronas-audited-financial-statements-2023 .audited-financial-img {
    margin: 20px auto 0px;
  }
}
@media screen and (min-device-width: 1200px) and (max-device-width: 1600px) and (-webkit-min-device-pixel-ratio: 2) and (-webkit-min-device-pixel-ratio: 2), screen and (min-device-width: 1200px) and (max-device-width: 1600px) and (-webkit-min-device-pixel-ratio: 2) and (min-resolution: 192dpi) {
  #home #petronas-audited-financial-statements-2023 .audited-financial-img {
    /* max-width: 440px;
    margin-top: 20px;
    margin-left: auto; */
    /* max-width: 300px; */
    max-width: 400px;
    margin-top: 20px;
  }
}
#home #sustainability-is-embedded-across-our-business {
  background-color: #f5f5f5;
  background-image: url("./../../assets/images/global/background.png");
  background-repeat: no-repeat;
  background-size: contain;
  padding: 200px 0px 100px 0px;
  background-position: right top;
}
@media (max-width: 639px) {
  #home #sustainability-is-embedded-across-our-business {
    padding: 100px 0px;
  }
}
#home #sustainability-is-embedded-across-our-business .section-title {
  /* margin-bottom: 80px; */
  margin-bottom: 50px;
  transition: all ease 500ms;
  --h1-font-size: 106px;
  --h1-line-height: 110px;
}
@media (max-width: 639px) {
  #home #sustainability-is-embedded-across-our-business .section-title {
    --h1-font-size: 55px;
    --h1-line-height: 59px;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 640px) and (min-device-height: 568px) and (max-device-height: 1136px) and (-webkit-device-pixel-ratio: 2) {
  #home #sustainability-is-embedded-across-our-business .section-title {
    /* Your custom styles for iPhone SE models */
    --h1-font-size: 45px;
    --h1-line-height: 49px;
  }
}
@media screen and (min-device-width: 1200px) and (max-device-width: 1600px) and (-webkit-min-device-pixel-ratio: 2) and (-webkit-min-device-pixel-ratio: 2), screen and (min-device-width: 1200px) and (max-device-width: 1600px) and (-webkit-min-device-pixel-ratio: 2) and (min-resolution: 192dpi) {
  #home #sustainability-is-embedded-across-our-business .section-title {
    --h1-font-size: 88px;
    --h1-line-height: 89px;
  }
}
#home #sustainability-is-embedded-across-our-business .section-title h2 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
#home #sustainability-is-embedded-across-our-business .section-title h2 span {
  color: var(--text-theme-color);
}
#home #key-messages {
  padding: 80px 0px 0px 0px;
}
#home #key-messages .section-title {
  text-align: center;
  transition: all ease 500ms;
}
#home #key-messages .section-title h2 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
#home #key-messages .section-title h2 span {
  color: var(--text-theme-color);
}
#home #key-messages .quote-flex {
  display: flex;
  align-items: center;
  height: 100%;
}
#home #key-messages .quote-content {
  display: flex;
  align-items: flex-start;
  flex-flow: column;
  gap: 50px;
}
@media (min-width: 640px) and (max-width: 1599px) {
  #home #key-messages .quote-content {
    margin: 60px 0px;
  }
}
@media (max-width: 639px) {
  #home #key-messages .quote-content {
    margin-top: 20px;
    gap: 40px;
  }
}
#home #key-messages .quote-content .quote-content-wrap {
  display: flex;
  flex-flow: column;
  gap: 10px;
}
#home #key-messages .quote-content .quote-content-wrap h5 {
  font-family: "Museo Sans";
  color: var(--text-theme-color);
  font-weight: 900;
  margin-top: -10px;
}
#home #key-messages .quote-content .quote-content-wrap h6.w-900 {
  font-family: "Museo Sans";
  color: var(--text-theme-color);
  font-weight: 900;
  margin-top: -10px;
}
#home #key-messages .quote-message {
  max-width: 500px;
  display: flex;
  align-items: flex-start;
  position: relative;
  --p-font-size: 20px;
  --p-line-height: 24px;
}
#home #key-messages .quote-img {
  width: 50px;
  height: 50px;
}
#home #key-messages .quote-img-open {
  margin-right: 20px;
}
#home #key-messages .quote-img-close {
  position: absolute;
  top: 100%;
  /* left: 90%; */
  left: 95%;
}
#home #key-messages .key-message-content {
  background: var(--background-light-grey-color);
  padding: 50px 0px;
}
@media (min-width: 640px) {
  #home #key-messages .key-message-content {
    padding: 0px;
  }
}
/* #home #key-messages .key-message-content .quote-wrapper {
  clip-path: rect(0% 110% 110% 100%);
  transition: all ease 500ms;
}
#home #key-messages .key-message-content .quote-content {
  clip-path: rect(0 0 110% 0);
  transition: all ease 500ms;
  transition-delay: 200ms;
} */
/* #home #key-messages .key-message-content.animated .quote-wrapper {
  clip-path: rect(0 110% 110% 0);
}
#home #key-messages .key-message-content.animated .quote-content {
  clip-path: rect(0 110% 110% 0);
} */
#home #key-messages .key-message-content:nth-child(even) {
  background: var(--background-white-color);
}
/* #home #key-messages .key-message-content:nth-child(even) .quote-wrapper {
  clip-path: rect(0 0 110% 0);
  transition: all ease 500ms;
} */
/* #home #key-messages .key-message-content:nth-child(even) .quote-content {
  clip-path: rect(0% 110% 110% 100%);
  transition: all ease 500ms;
  transition-delay: 200ms;
} */
/* #home #key-messages .key-message-content:nth-child(even).animated .quote-wrapper {
  clip-path: rect(0 110% 110% 0);
}
#home #key-messages .key-message-content:nth-child(even).animated .quote-content {
  clip-path: rect(0 110% 110% 0);
} */
#home #business-highlights {
  background: var(--background-white-color);
  padding: 100px 0px;
}
#home #business-highlights .swiper-content-desc {
  height: 100%;
  margin-top: 30px;
}
@media (min-width: 1440px) {
  #home #business-highlights .swiper-content-desc {
    margin-top: 0px;
    height: 100%;
  }
}
#home #business-highlights .swiper-box {
  position: relative;
}
@media (min-width: 640px) {
  #home #business-highlights .swiper-box {
    position: static;
  }
}
#home #business-highlights .ratio {
  --bs-aspect-ratio: 100%;
}
@media (max-width: 639px) {
  #home #business-highlights .ratio > * {
    position: relative;
    height: unset;
  }
  #home #business-highlights .ratio::before {
    display: none;
  }
}
@media (min-width: 1440px) {
  #home #business-highlights .ratio {
    --bs-aspect-ratio: 70%;
  }
}
@media (min-width: 1600px) {
  #home #business-highlights .ratio {
    --bs-aspect-ratio: 76.457143%;
  }
}
#home #business-highlights .swiper-item {
  display: flex;
  align-items: center;
}
@media (min-width: 640px) {
  #home #business-highlights .swiper-item {
    height: 100%;
  }
}
#home #business-highlights .swiper-items-business-highlight {
  position: relative;
}
@media (min-width: 640px) {
  #home #business-highlights .swiper-items-business-highlight {
    padding-right: 80px;
  }
}
#home #business-highlights .swiper-button-controls {
  position: absolute;
  top: 0px;
  right: 0px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  z-index: 2;
  gap: 10px;
}
@media (min-width: 640px) {
  #home #business-highlights .swiper-button-controls {
    right: 0px;
    left: unset;
    top: 50%;
    gap: 20px;
    transform: translateY(-50%);
    justify-content: unset;
    flex-direction: column;
  }
}
#home #business-highlights .swiper-button-controls .swiper-button-up,
#home #business-highlights .swiper-button-controls .swiper-button-down {
  background: unset;
  padding: unset;
  border: unset;
  outline: unset !important;
  max-width: 60px;
  box-shadow: unset !important;
  z-index: 1;
}
@media (max-width: 639px) {
  #home #business-highlights .swiper-button-controls .swiper-button-up,
  #home #business-highlights .swiper-button-controls .swiper-button-down {
    max-width: 50px;
  }
}
@media (max-width: 639px) {
  #home #business-highlights .swiper-button-controls .swiper-button-up img,
  #home #business-highlights .swiper-button-controls .swiper-button-down img {
    transform: rotate(-90deg);
  }
}
#home #business-highlights .section-title {
  text-align: center;
  display: flex;
  flex-flow: column;
  gap: 50px;
  max-width: 1300px;
  margin: 0 auto;
  margin-bottom: 100px;
  transition: all ease 500ms;
}
#home #business-highlights .section-title .sub-section-content {
  text-align: center;
  display: flex;
  flex-flow: column;
  gap: 20px;
}
#home #business-highlights .section-title h2 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
#home #business-highlights .section-title h2 span {
  color: var(--text-theme-color);
}
#home #business-highlights .section-title h5 {
  font-family: "Museo Sans";
  font-weight: 700;
  color: var(--text-black-color);
}
#home #business-highlights .section-title h5 span {
  color: var(--text-theme-color);
}
#home #business-highlights .section-title h6 {
  font-family: "Museo Sans";
  font-weight: 500;
  max-width: 990px;
  margin: 0 auto;
}
#home #business-highlights .section-title p {
  font-family: "Museo Sans";
  font-weight: 500;
  max-width: 800px;
  margin: 0 auto;
}
#home #business-highlights .swiper-title {
  opacity: 0;
  position: absolute;
  top: 45px;
  left: 0;
  width: 100%;
  animation-duration: 0.5s;
  animation-fill-mode: both;
  animation-delay: 0.2s;
  max-width: 500px;
  vertical-align: bottom;
}
#home #business-highlights .swiper-title.active {
  opacity: 1;
  position: relative;
  top: 0;
  animation-name: fadeInUp;
}
#home #business-highlights .business-desc {
  display: none;
  opacity: 0;
  animation-duration: 0.5s;
  animation-fill-mode: both;
  animation-delay: 0.2s;
}
#home #business-highlights .business-desc.active {
  display: block;
  animation-name: fadeInUp;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: none;
  }
}
@keyframes fadeOutDown {
  from {
    opacity: 1;
    transform: none;
  }
  to {
    opacity: 0;
    transform: translateY(50px);
  }
}
@media (max-width: 639px) {
  #home #business-highlights .swiper-title-wrapper {
    max-width: 210px;
    display: flex;
    align-items: flex-end;
    width: 100%;
  }
}
#home #business-highlights .swiper-title-wrapper h5 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-theme-color);
}
#home #business-highlights .swiper-desc {
  display: flex;
  flex-flow: column;
  align-items: flex-start;
  gap: 20px;
  margin: 40px 0px;
  --p-font-size: 20px;
  --line-height: 24px;
}
#home #business-highlights .business-highlight-img {
  width: 100%;
  max-height: 480px;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}
@media (max-width: 639px) {
  #home #business-highlights .business-highlight-img {
    margin-bottom: 30px;
    max-height: 280px;
  }
}
@media (min-width: 1600px) {
  #home #business-highlights .business-highlight-img {
    max-height: 580px;
  }
}
#home #business-highlights .business-highlight-img img {
  -o-object-position: center;
     object-position: center;
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 480px;
}
@media (max-width: 639px) {
  #home #business-highlights .business-highlight-img img {
    height: 280px;
  }
}
@media (min-width: 1600px) {
  #home #business-highlights .business-highlight-img img {
    height: 580px;
  }
}
#home #our-sutainability-journey .section-title {
  /* max-width: 500px; */
  max-width: 590px;
  transition: all ease 500ms;
}
#home #our-sutainability-journey .section-title h2 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
@media (max-width: 639px) {
  #home #our-sutainability-journey .section-title h2 {
    --h1-font-size: 55px;
    --h1-line-height: 59px;
  }
}
#home #our-sutainability-journey .section-title h2 span {
  color: var(--text-theme-color);
}
#home #our-sutainability-journey .timeline-mobile {
  display: block;
}
@media (min-width: 640px) {
  #home #our-sutainability-journey .timeline-mobile {
    display: none;
  }
}
#home #our-sutainability-journey .timeline-desktop {
  display: none;
}
@media (min-width: 640px) {
  #home #our-sutainability-journey .timeline-desktop {
    display: block;
  }
}
#home #our-sutainability-journey .tl-item:hover {
  width: 100% !important;
}

#home #our-sutainability-journey .our-sutainability-journey-wrapper {
  padding: 50px 0px 100px 0px;
  background: url("./../../assets/images/our-sutainability-journey/bg-1.png");
  background-repeat: no-repeat;
  /* background-size: auto 118%;
  background-position: 100% -45px; */
  background-size: auto 139%;
  background-position: 100% -114px;
  /* background-position: 0% -114px; */
}
@media (max-width: 1920px) {
  #home #our-sutainability-journey .our-sutainability-journey-wrapper {
    background-size: auto 164%;
    background-position: 50% -200px;
  }
}
@media (max-width: 1440px) {
  #home #our-sutainability-journey .our-sutainability-journey-wrapper {
    background-size: auto 139%;
    /* background-position: 50% -200px; */
}
}
@media (min-width: 640px) {
  #home #our-sutainability-journey .our-sutainability-journey-wrapper {
    /* padding: 190px 0px 140px 0px; */
    padding: 190px 0px 50px 0px;
  }
}
#home #our-sutainability-journey .our-sutainability-video-wrapper {
  background: url("./../../assets/images/our-sutainability-journey/bg-2.png");
  background-repeat: no-repeat;
  background-size: cover;
}
@media (max-width: 639px) {
  #home #our-sutainability-journey .our-sutainability-video-wrapper {
    background: unset;
  }
}
#home #our-sutainability-journey .our-sutainability-item-wrapper {
  padding: 50px 0px;
  background: url("./../../assets/images/our-sutainability-journey/bg-3.png");
  background-repeat: no-repeat;
  background-size: contain;
  /* background-position: bottom left; */
  background-position: right;
}
@media (min-width: 640px) and (max-width: 1599px) {
  #home #our-sutainability-journey .our-sutainability-item-wrapper h4 {
    font-size: 28px;
  }
}
@media (min-width: 640px) {
  #home #our-sutainability-journey .our-sutainability-item-wrapper {
    padding-bottom: 100px;
    padding-top: 120px;
  }
}
@media (min-width: 1440px) {
  #home #our-sutainability-journey .our-sutainability-item-wrapper {
    /* padding: 200px 0px 400px 0px; */
    padding: 200px 0px 200px 0px;
  }
}
#home #our-sutainability-journey .sutainability-content-1 {
  display: flex;
  flex-flow: column;
  gap: 30px;
  max-width: 835px;
  text-align: center;
  margin: 0 auto;
}
#home #our-sutainability-journey .sutainability-content-2 {
  clip-path: rect(0 0% 100% 0%);
}
#home #our-sutainability-journey .sutainability-content-2,
#home #our-sutainability-journey .sutainability-content-3 {
  display: flex;
  flex-flow: column;
  gap: 20px;
  justify-content: center;
  align-items: flex-start;
  height: 100%;
  --p-font-size: 20px;
  --p-line-height: 24px;
}
#home #our-sutainability-journey .sutainability-content-2 h5,
#home #our-sutainability-journey .sutainability-content-3 h5 {
  font-family: "Museo Sans";
  color: var(--text-black-color);
  font-weight: 700;
}
#home #our-sutainability-journey .sutainability-content-2 h5 span,
#home #our-sutainability-journey .sutainability-content-3 h5 span {
  color: var(--text-theme-color);
}
#home #our-sutainability-journey .sutainability-content-2 p,
#home #our-sutainability-journey .sutainability-content-3 p {
  font-family: "Museo Sans";
  color: var(--text-black-color);
}
#home #our-sutainability-journey .sutainability-content-3 {
  clip-path: rect(0 100% 100% 100%);
}
#home #our-sutainability-journey .sutainability-content-3 p {
  font-family: "Museo Sans";
  color: var(--text-black-color);
}
#home #our-sutainability-journey .sutainability-content-2,
#home #our-sutainability-journey .sutainability-content-3 {
  transition: all ease 500ms;
}
#home #our-sutainability-journey .sutainability-content-2.animated,
#home #our-sutainability-journey .sutainability-content-3.animated {
  clip-path: rect(0 100% 100% 0);
}
#home #our-sutainability-journey .video-sutainbility-group {
  position: relative;
}
@media (max-width: 639px) {
  #home #our-sutainability-journey .video-sutainbility-group {
    transform: unset !important;
  }
}
#home #our-sutainability-journey .video-sutainbility-group.animated .perspective-video {
  transform: perspective(1400px) rotateY(0deg) translateY(0px) rotateX(0deg) scale(1) translateX(0px);
}
#home #our-sutainability-journey .video-sutainbility-group .video-slide {
  transform: scale(0.95) translate(30px, 0px);
  z-index: 0;
  transition: all ease 500ms;
}
@media (min-width: 640px) {
  #home #our-sutainability-journey .video-sutainbility-group .video-slide {
    transform: scale(0.95) translate(50px, 0px);
  }
}
#home #our-sutainability-journey .video-sutainbility-group .video-slide .perspective-video {
  pointer-events: none;
}
#home #our-sutainability-journey .video-sutainbility-group .video-slide.active {
  transform: scale(1) translate(0px, 30px);
  z-index: 1;
}
@media (min-width: 640px) {
  #home #our-sutainability-journey .video-sutainbility-group .video-slide.active {
    transform: scale(1) translate(0px, 50px);
  }
}
#home #our-sutainability-journey .video-sutainbility-group .video-slide.active .perspective-video {
  pointer-events: inherit;
}
#home #our-sutainability-journey .video-sutainbility-group .perspective-video {
  transform: perspective(1400px) rotateY(-20deg) scale(0.7) rotateX(15deg) translateX(-160px);
  transition: transform ease 500ms;
  transition-delay: 500ms;
  clip-path: inset(0px round 10px);
}
@media (max-width: 639px) {
  #home #our-sutainability-journey .video-sutainbility-group .perspective-video {
    transform: unset !important;
  }
}
@media (min-width: 640px) {
  #home #our-sutainability-journey .video-sutainbility-group .perspective-video {
    clip-path: inset(0px round 40px);
  }
}

#home #our-sutainability-journey .sustainability-listing .sustainability-list:first-child::before {
  content: "";
  background: rgb(0, 0, 0);
  top: 0px;
  left: 0px;
  position: absolute;
  width: 100%;
  height: 2px;
}

#home #our-sutainability-journey .sustainability-listing .sustainability-list .sustainability-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  color: var(--text-black-color);
  transition: all ease 500ms;
  padding: 20px;
  z-index: 0;
  position: relative;
}

@media screen and (max-width:430px) {
  #home #our-sutainability-journey .sustainability-listing .sustainability-list .sustainability-content {
    font-size: 16px;
    line-height: 20px;
    padding: 12px;
  }

}

#home #our-sutainability-journey .sustainability-listing .sustainability-list .sustainability-content::before {
  content: "";
  background-image: var(--sustainability-background-image, url(./../../assets/images/our-sutainability-journey/net-zero.png));
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  transition: all ease 1s;
  display: block;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: all ease 500ms;
  z-index: -1;
}

#home #our-sutainability-journey .sustainability-listing .sustainability-list .sustainability-content::after {
  content: "";
  background: url(./../../assets/images/our-sutainability-journey/arrow-black.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: right center;
  width: 30px;
  height: 30px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

#home #our-sutainability-journey .sustainability-listing .sustainability-list::after {
  content: "";
  background: rgb(0, 0, 0);
  bottom: 0px;
  left: 0px;
  position: absolute;
  width: 100%;
  height: 2px;
}

#home #our-sutainability-journey .sustainability-listing .sustainability-list {
  position: relative;
  text-decoration: none;
  display: block;
}

@media (min-width: 640px) {
  #home #our-sutainability-journey .sustainability-listing .sustainability-list:hover .sustainability-content {
      color: var(--text-white-color);
  }

  #home #our-sutainability-journey .sustainability-listing .sustainability-list:hover .sustainability-content::before {
    opacity: 1;
  }

  #home #our-sutainability-journey .sustainability-listing .sustainability-list:hover .sustainability-content::after {
    background-image: url(./../../assets/images/our-sutainability-journey/arrow-white.png);
  }
}

#home #net-zero-carbon-emissions {
  background: linear-gradient(to bottom, #f0f0f0, #ffffff);
  padding: 50px 0px;
}
@media (min-width: 640px) {
  #home #net-zero-carbon-emissions {
    padding: 100px 0px;
  }
}
#home #net-zero-carbon-emissions .section-title {
  display: flex;
  flex-flow: column;
  text-align: center;
  gap: 20px;
  max-width: 1122px;
  margin: auto;
}
#home #net-zero-carbon-emissions .section-title h5 {
  font-family: "Museo Sans";
  color: var(--text-black-color);
  font-weight: 700;
}
#home #net-zero-carbon-emissions .section-title h5 span {
  color: var(--text-theme-color);
}
#home #net-zero-carbon-emissions .section-title h6 {
  font-family: "Museo Sans";
  font-weight: 300;
}
#home #net-zero-carbon-emissions .section-title h6 strong {
  font-weight: 900;
}
#home #net-zero-carbon-emissions .net-zero-img {
  margin: 40px 0px;
}
@media (min-width: 640px) {
  #home #net-zero-carbon-emissions .net-zero-img {
    margin: 80px 0px;
  }
}
#home #net-zero-carbon-emissions .sustainability-listing .sustainability-list {
  position: relative;
  text-decoration: none;
  display: block;
}
#home #net-zero-carbon-emissions .sustainability-listing .sustainability-list .sustainability-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  color: var(--text-black-color);
  transition: all ease 500ms;
  padding: 20px;
  z-index: 0;
  position: relative;
}
@media (max-width: 639px) {
  #home #net-zero-carbon-emissions .sustainability-listing .sustainability-list .sustainability-content {
    font-size: 16px;
    line-height: 20px;
    padding-right: 40px;
  }
}
#home #net-zero-carbon-emissions .sustainability-listing .sustainability-list .sustainability-content::before {
  content: "";
  background-image: var(--sustainability-background-image, url("./../../assets/images/our-sutainability-journey/net-zero.png"));
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  transition: all ease 1s;
  display: block;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: all ease 500ms;
  z-index: -1;
}
#home #net-zero-carbon-emissions .sustainability-listing .sustainability-list .sustainability-content::after {
  content: "";
  background: url("./../../assets/images/our-sutainability-journey/arrow-black.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: right center;
  width: 30px;
  height: 30px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
@media (max-width: 639px) {
  #home #net-zero-carbon-emissions .sustainability-listing .sustainability-list .sustainability-content::after {
    width: 20px;
    height: 20px;
  }
}
@media (min-width: 640px) {
  #home #net-zero-carbon-emissions .sustainability-listing .sustainability-list:hover .sustainability-content {
    color: var(--text-white-color);
  }
  #home #net-zero-carbon-emissions .sustainability-listing .sustainability-list:hover .sustainability-content::after {
    background-image: url("./../../assets/images/our-sutainability-journey/arrow-white.png");
  }
  #home #net-zero-carbon-emissions .sustainability-listing .sustainability-list:hover .sustainability-content::before {
    opacity: 1;
  }
}
#home #net-zero-carbon-emissions .sustainability-listing .sustainability-list img {
  width: 50px;
}
#home #net-zero-carbon-emissions .sustainability-listing .sustainability-list:first-child::before {
  content: "";
  background: rgb(0, 0, 0);
  top: 0px;
  left: 0px;
  position: absolute;
  width: 100%;
  height: 2px;
}
#home #net-zero-carbon-emissions .sustainability-listing .sustainability-list::after {
  content: "";
  background: rgb(0, 0, 0);
  bottom: 0px;
  left: 0px;
  position: absolute;
  width: 100%;
  height: 2px;
}
#home #net-zero-carbon-emissions .section-content {
  height: 100%;
  display: flex;
  flex-flow: row;
  align-items: center;
  max-width: 700px;
  margin-left: auto;
}
#home #net-zero-carbon-emissions .section-content h6 {
  font-family: "Museo Sans";
  color: var(--text-black-color);
}
#home #human-rights-policy {
  padding: 50px 0px;
}
#home #human-rights-policy .ratio {
  --bs-aspect-ratio: 60%;
}
@media (min-width: 640px) {
  #home #human-rights-policy .ratio {
    --bs-aspect-ratio: 30%;
  }
}
@media (min-width: 1440px) {
  #home #human-rights-policy .ratio {
    --bs-aspect-ratio: 20%;
  }
}
@media (min-width: 640px) {
  #home #human-rights-policy {
    padding: 100px 0px;
  }
}
#home #human-rights-policy .chart-sustainability {
  position: relative;
}
#home #human-rights-policy .chart-sustainability .chart-total {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  position: absolute;
  height: 100%;
  right: 0px;
  top: 0px;
  opacity: 0;
  padding: 5px 0px;
  z-index: 1;
}
#home #human-rights-policy .chart-sustainability .chart-total p {
  font-family: "Museo Sans";
  font-weight: 500;
  color: var(--text-black-color);
}
#home #human-rights-policy .chart-sustainability .chart-total p.bold {
  font-weight: 900;
}
#home #human-rights-policy .chart-sustainability .chart-total.animated {
  opacity: 1;
}
#home #human-rights-policy .legend-info {
  margin-bottom: 20px;
}
#home #human-rights-policy .legend-info p {
  font-family: "Museo Sans";
  font-weight: 500;
  color: var(--text-black-color);
}
#home #human-rights-policy .sub-section-title {
  display: flex;
  flex-flow: column;
  gap: 5px;
  margin-bottom: 50px;
}
#home #human-rights-policy .sub-section-title h5 {
  font-family: "Museo Sans";
  color: var(--text-black-color);
  font-weight: 700;
}
#home #human-rights-policy .sub-section-title h5 span {
  color: var(--text-theme-color);
}
#home #human-rights-policy .sub-section-title h6 {
  font-family: "Museo Sans";
  font-weight: 500;
  color: var(--text-black-color);
}
#home #human-rights-policy .legend-canvas-wrapper {
  display: flex;
  flex-flow: wrap;
  gap: 5px 20px;
  justify-content: center;
}
@media (min-width: 640px) {
  #home #human-rights-policy .legend-canvas-wrapper {
    gap: 10px 40px;
  }
}
#home #human-rights-policy .legend-canvas-wrapper .legend-canvas {
  display: flex;
  flex-flow: wrap;
  gap: 10px;
  align-items: flex-end;
}
#home #human-rights-policy .legend-canvas-wrapper .legend-canvas .legend-box {
  height: 25px;
  width: 25px;
  background-color: var(--legend-background-color);
}
@media (min-width: 640px) {
  #home #human-rights-policy .legend-canvas-wrapper .legend-canvas .legend-box {
    height: 40px;
    width: 40px;
  }
}
#home #human-rights-policy .legend-canvas-wrapper .legend-canvas p {
  font-family: "Museo Sans";
  font-size: 26px;
  color: var(--text-black-color);
  font-weight: 500;
}
@media (max-width: 639px) {
  #home #human-rights-policy .legend-canvas-wrapper .legend-canvas p {
    font-size: 18px;
  }
}
#home #human-rights-policy canvas {
  max-width: 100%;
  width: 100%;
}
#home #human-rights-policy .notes-wrapper {
  margin-top: 100px;
}
#home #human-rights-policy .notes-wrapper p {
  font-family: "Museo Sans";
  color: var(--text-black-color);
}
#home #human-rights-policy .notes-wrapper ul li {
  font-family: "Museo Sans";
  color: var(--text-black-color);
}
#home #human-rights-policy .section-title {
  text-align: center;
  max-width: 960px;
  display: flex;
  flex-flow: column;
  gap: 20px;
  margin: 0 auto;
}
#home #human-rights-policy .section-title h5 {
  font-family: "Museo Sans";
  font-weight: 700;
  color: var(--text-black-color);
}
#home #human-rights-policy .section-title h5 span {
  color: var(--text-theme-color);
}
#home #human-rights-policy .section-title p {
  font-family: "Museo Sans";
}
#home #human-rights-policy .accordion-content {
  padding: 100px 0px;
}
#home #non-financial-content-index {
  /* background: url("./../../assets/images/non-financial-content-index/vector.png"), linear-gradient(to bottom, #f0f0f0, #ffffff); */
  /* background: url("./../../assets/images/non-financial-content-index/vector-6.png"), linear-gradient(to bottom, #f0f0f0, #ffffff); */
  background-position: top 29px right 150px, right;
  background-repeat: no-repeat;
  /* background-size: 30% auto, contain; */
  background-size: 23% auto, contain;
  padding: 100px 0px;
}
@media (max-width: 639px) {
  #home #non-financial-content-index {
    padding-bottom: 50px;
    background-size: 300px auto, contain;
    background-position: top 0px right 0px, right;
  }
}
#home #non-financial-content-index .main-section-title {
  padding: 100px 0px 70px 0px;
  text-align: center;
}
@media (max-width: 639px) {
  #home #non-financial-content-index .main-section-title {
    padding: 0px 0px 70px 0px;
  }
}
#home #non-financial-content-index .main-section-title h2 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
#home #non-financial-content-index .main-section-title h2 span {
  color: var(--text-theme-color);
}
#home #non-financial-content-index .section-title {
  max-width: 940px;
  margin: 0 auto;
  display: flex;
  flex-flow: column;
  gap: 30px;
  --p-font-size: 20px;
  --p-line-height: 24px;
}
#home #non-financial-content-index .section-title h5 {
  font-family: "Museo Sans";
  font-weight: 700;
  color: var(--text-black-color);
  text-align: center;
  transition: all ease 500ms;
}
#home #non-financial-content-index .section-title p {
  font-family: "Museo Sans";
  text-align: center;
  transition: all ease 500ms;
}
#home #non-financial-content-index .btn-secondary {
  --btn-margin-top: 30px;
  margin-bottom: 80px;
}
#home #integrated-report-2023 {
  min-height: unset;
  padding: 100px 0px;
}
#home #integrated-report-2023 .swiper-integrated-report {
  padding-bottom: 50px;
}
#home #integrated-report-2023 .section-title {
  height: 100%;
  display: flex;
  flex-flow: column;
  gap: 30px;
  justify-content: center;
  align-items: flex-start;
}
@media (max-width: 639px) {
  #home #integrated-report-2023 .section-title {
    --h1-font-size: 55px;
    --h1-line-height: 59px;
  }
}
@media (max-width: 1479px) {
  #home #integrated-report-2023 .section-title {
    /* max-width: 400px; */
    max-width: 550px;
    width: 100%;
  }
}
#home #integrated-report-2023 .section-title h2 {
  font-family: "Museo Sans";
  font-weight: 900;
  color: var(--text-black-color);
}
#home #integrated-report-2023 .section-title h2 span {
  color: var(--text-theme-color);
}
#home #integrated-report-2023 .section-title h6 {
  font-family: "Museo Sans";
  font-weight: 500;
  color: var(--text-black-color);
  margin-left: 10px;
}
#home #integrated-report-2023 .vector-wrapper {
  background: url("./../../assets/images/integrated-report-2023/vector.png");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}
#home #integrated-report-2023 .vector-wrapper .img-wrapper {
  /* max-width: 400px; */
  max-width: 430px;
  margin: auto;
}

#reports {
  padding: 110px 0px;
}
#reports .form-label {
  text-align: left;
  width: 100%;
}
#reports .btn-group {
  display: flex !important;
}
#reports .filter-toggle-wrapper {
  text-align: right;
  max-width: 1200px;
  margin: 0 auto;
  padding: 25px 0;
}
#reports .filter-toggle-wrapper .toggle-switch {
  position: relative;
  display: inline-block;
  width: 38px;
  height: 20px;
}
#reports .filter-toggle-wrapper .toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
#reports .filter-toggle-wrapper .toggle-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 10px;
  transition: 0.4s;
}
#reports .filter-toggle-wrapper .toggle-switch .slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: 0.4s;
}
#reports .filter-toggle-wrapper .toggle-switch input:checked + .slider {
  background-color: #00a19c;
}
#reports .filter-toggle-wrapper .toggle-switch input:checked + .slider:before {
  transform: translateX(16px);
}
#reports section#reports-section .mb-15 {
  margin-bottom: 15px;
}
#reports section#reports-section .interactive-chart {
  margin-left: auto;
  margin-right: auto;
  min-height: 60vh;
}
@media (min-width: 576px) {
  #reports section#reports-section .interactive-chart {
    margin-top: 10px;
    margin-bottom: 50px;
  }
}
#reports section#reports-section .dot {
  height: 15px;
  width: 15px;
  background-color: #fabb2c;
  border-radius: 50%;
  display: inline-block;
  margin-right: 10px;
  margin-bottom: -2px;
}
#reports section#reports-section .theme-green,
#reports section#reports-section .link,
#reports section#reports-section a:hover,
#reports section#reports-section .nav-link:hover,
#reports section#reports-section .cta a,
#reports section#reports-section .cta a p {
  color: #00a19c;
}
#reports section#reports-section a,
#reports section#reports-section a:focus,
#reports section#reports-section a:hover,
#reports section#reports-section a:active,
#reports section#reports-section a:visited,
#reports section#reports-section button,
#reports section#reports-section button:focus,
#reports section#reports-section button:hover,
#reports section#reports-section button:active,
#reports section#reports-section button:visited,
#reports section#reports-section .btn-close,
#reports section#reports-section .btn-close:hover,
#reports section#reports-section .btn-close:active,
#reports section#reports-section .btn-close:focus,
#reports section#reports-section .btn-close:visited,
#reports section#reports-section .navbar-toggler,
#reports section#reports-section .navbar-toggler:focus,
#reports section#reports-section .navbar-toggler:visited,
#reports section#reports-section .navbar-toggler:hover,
#reports section#reports-section .navbar-toggler:active {
  outline: none;
  text-decoration: none;
  box-shadow: none;
}
#reports section#reports-section .title-pet {
  display: inline-block;
  margin-bottom: 10px;
  color: #8ea8ba;
  margin-bottom: 20px;
}
#reports section#reports-section .error {
  display: none;
  position: absolute;
  width: 100%;
  top: 100%;
  color: #ff0000;
  font-size: 16px;
  line-height: 1;
  padding: 10px 10px 5px;
}
#reports section#reports-section .error.success {
  color: #28a745;
}
#reports section#reports-section input.processing {
  background: #979797;
  pointer-events: none;
}
#reports section#reports-section .bracket {
  display: inline-block;
}
#reports section#reports-section img {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  -webkit-user-drag: none;
}
#reports section#reports-section .row.equal-height {
  align-items: stretch !important;
}
#reports section#reports-section .chart {
  position: relative;
  width: 100%;
}
@media screen and (min-width: 768px) and (max-width: 1024px) {
  #reports section#reports-section .chart {
    max-width: 650px;
  }
}
@media screen and (min-width: 1025px) and (max-width: 1280px) {
  #reports section#reports-section .chart {
    max-width: 900px;
  }
}
@media screen and (min-width: 1281px) {
  #reports section#reports-section .chart {
    max-width: 1200px;
  }
}
#reports section#reports-section .dropdown-check-list {
  display: inline-block;
  background-color: #ffffff;
}
#reports section#reports-section .dropdown-check-list .anchor {
  position: relative;
  cursor: pointer;
  display: inline-block;
  padding: 14px 44px 19px 13px;
  border: 1px solid #ccc;
}
#reports section#reports-section .dropdown-check-list .anchor:after {
  position: absolute;
  content: "";
  border-left: 2px solid black;
  border-top: 2px solid black;
  padding: 5px;
  right: 10px;
  top: 30%;
  transform: rotate(-135deg);
}
#reports section#reports-section .dropdown-check-list .anchor:active:after {
  right: 8px;
  top: 21%;
}
#reports section#reports-section .dropdown-check-list ul.items {
  padding: 2px;
  display: none;
  margin: 0;
  border: 1px solid #ccc;
  border-top: none;
}
#reports section#reports-section .dropdown-check-list ul.items li {
  list-style: none;
}
#reports section#reports-section .dropdown-check-list.visible .anchor {
  color: #0094ff;
}
#reports section#reports-section .dropdown-check-list.visible .items {
  display: block;
  position: absolute;
  background: #fff;
  border: 1px solid #ccc;
  border-top-left-radius: 0;
}
#reports section#reports-section .filter-nav {
  display: block;
  width: 100%;
  padding: 25px 0;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}
#reports section#reports-section #yearlist ul.items {
  width: 700px;
  border-top: none;
  padding: 10px;
}
#reports section#reports-section #speedlist ul.items {
  width: 400px;
  border-top: none;
  padding: 10px;
}
@media screen and (min-width: 768px) and (max-width: 991px) {
  #reports section#reports-section p,
  #reports section#reports-section a,
  #reports section#reports-section span,
  #reports section#reports-section li,
  #reports section#reports-section input,
  #reports section#reports-section textarea,
  #reports section#reports-section select,
  #reports section#reports-section label,
  #reports section#reports-section div,
  #reports section#reports-section th,
  #reports section#reports-section td,
  #reports section#reports-section dt,
  #reports section#reports-section dd {
    font-size: 14px;
    line-height: 20px;
  }
}
@media screen and (min-width: 768px) {
  #reports section#reports-section .social-media-list {
    justify-content: flex-end;
  }
}
@media screen and (max-width: 767px) {
  #reports section#reports-section .mobile-dot {
    display: block;
    position: relative;
    margin-right: 15px;
    width: 24px;
    height: 24px;
  }
  #reports section#reports-section .mobile-dot,
  #reports section#reports-section .mobile-dot::after {
    width: 24px;
    height: 24px;
  }
  #reports section#reports-section .mobile-dot::before,
  #reports section#reports-section .mobile-dot::after {
    content: "";
    display: block;
    position: absolute;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  #reports section#reports-section .mobile-dot::before {
    width: 10px;
    height: 10px;
    background-color: #ffffff;
  }
  #reports section#reports-section .mobile-dot::after {
    border: 2px solid #ffffff;
  }
}
@media screen and (max-width: 767px) {
  #reports section#reports-section .mobile-height {
    height: 1200px;
  }
  #reports section#reports-section .mobile-words-padding {
    padding-top: 0px;
    padding-left: 50px !important;
    padding-right: 50px !important;
  }
  #reports section#reports-section .pet-report-padding {
    padding: 50px 25px !important;
  }
  #reports section#reports-section .reports-padding {
    margin-left: 12% !important;
  }
  #reports section#reports-section .dropdown-check-list .anchor {
    position: relative;
    cursor: pointer;
    display: inline-block;
    padding: 14px 39px 19px 13px;
    border: 1px solid #ccc;
  }
  #reports section#reports-section .mobile-width-chart-years {
    width: 152px !important;
  }
  #reports section#reports-section .mobile-width-chart-indicators {
    width: 191px !important;
  }
  #reports section#reports-section #speedlist ul.items {
    width: 152px;
    border-top: none;
    padding: 10px;
  }
  #reports section#reports-section #yearlist ul.items {
    width: 200px;
    border-top: none;
    padding: 10px;
  }
  #reports section#reports-section .mobile-profit-padding {
    position: absolute;
  }
  #reports section#reports-section .mobile-padding-cffo {
    margin-top: 25px;
  }
  #reports section#reports-section .mobile-flex-padding {
    display: flex;
  }
}

.bullet-cus ul {
  list-style-type: none;
  margin: 0;
}

.bullet-cus ul li {
  position: relative;
  margin: 1em 0;
  display: flex;
  /* align-items: center;
  font-size: 14px;
  color: #6D6D6D; */
}

.bullet-cus ul li::before {
  content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='27' height='27' viewBox='0 0 27 27'%3E%3Cg id='Group_22253' data-name='Group 22253' transform='translate(-4397 541)'%3E%3Ccircle id='Ellipse_966' data-name='Ellipse 966' cx='13.5' cy='13.5' r='13.5' transform='translate(4397 -541)' fill='%23fdb924'/%3E%3Cpath id='Icon_akar-arrow-right' data-name='Icon akar-arrow-right' d='M6,13.628H20.006M13.878,7.5l6.128,6.128-6.128,6.128' transform='translate(4397.409 -541.215)' fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'/%3E%3C/g%3E%3C/svg%3E%0A");
  height: 20px; width: 20px;
  transform: translatex(-50%);
  margin-right: 10px;
}




/* PIR 2024 */
body{
  overflow-x: hidden !important;
}
.read-more-cta{
    min-width: 200px;
    display: flex;
    flex-direction: column;
    text-decoration: none;
    width: auto;
    margin: 0;
    z-index: 1;
}
.read-more-cta span {
  display: inline-flex;
  align-items: center;
  gap: 25px;
  font-size: 18px;
  color: #ffffff;
  text-decoration: none;
  font-weight: 300;
  justify-content: space-between;
  opacity:0.8;
  transition: opacity 1s ease;
}

.read-more-cta.green span{
  color: #00A19C;
  /* background-image: linear-gradient(90deg, var(--text-black-color) var(--highlight-offset), #00A19C var(--highlight-offset)); */
}
.read-more-cta.purple span{
  color: #5b5b5b;
}

.read-more-cta svg {
  display: inline-block;
  width: 25px;
  height: 25px;
  transition: transform 0.5s ease;
  background-position: center;
  margin-right: 55px;
}
.read-more-cta:hover span {
    opacity: 1;
    color: #ffffff;
}
.read-more-cta.purple:hover span{
 opacity: 1;
    color: #75409A;
}
.read-more-cta.green:hover span {
    color: #00A19C;
}
.read-more-cta.purple:hover span {
    color: #75409A ;
}
.read-more-cta:hover svg {
  transform: translateX(55px);
}
.read-more-cta .cta-line{
  height: 1px;
  width: 100%;
  background-color: #ffffff45;
  margin-top: 12px;
}
.read-more-cta.green .cta-line{
  background-color: #00a19c63;
}
.read-more-cta.purple .cta-line{
  background-color: #75409a56;
}
.read-more-cta .cta-line::before{
  height: 1px;
  width: 1%;
  background-color: #ffffff;
  content: "";
  display: block;
  transition: all 0.5s linear;
}
.read-more-cta.green .cta-line::before, .read-more-cta.green:hover .cta-line::before{
  background-color: #00A19C;
}
.read-more-cta.purple .cta-line::before, .read-more-cta.purple:hover .cta-line::before{
  background-color: #75409A !important;
}
.read-more-cta:hover .cta-line::before{
  width: 100%;
  background-color: #ffffff;
}


@media screen and (max-width: 1028px) {
  .read-more-cta span{
    font-size: 16px;
  }
}

.home{
      overflow: hidden !important;
}
#navbar{
  background:transparent;
}
#navbar.hide{
  opacity: 0;
  visibility: hidden;
}
#navbar.show{
  background: #ffffff;
}
.sticky-nav-bar{
  background-color: var(--background-light-theme-color);
}
@media screen and (max-width: 1024px) {
  body .navbar .navbar-brand img{
    height: 70px;
  }
    body .navbar .navbar-brand {
      padding-top: 10px;
      padding-bottom: 10px;
  }
}

.pir-2024-cover{
  object-fit: cover;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.desktop-image, .hide-mobile {
  display: block;
}
.mobile-image, .hide-desktop {
  display: none;
}

@media screen and (max-width: 1024px) {
  .desktop-image, .hide-mobile  {
    display: none;
  }
  .mobile-image, .hide-desktop{
    display: block;
  }
}

.line-animation-columns-container {
  width: 100%;
}

.line-animation-section {
  position: relative;
  height: 100vh;
  display: flex;
  background-color: white;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  overflow: hidden;
}

.line-animation-video, .line-animation-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 102%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.line-animation-panel-wrapper {
  display: flex;
  flex-direction: row;
  position: absolute;
  z-index: 2;
  height: 100%;
  width: 100%;
}

.line-animation-panel {
  height: 100%;
  width: 25%;
  overflow: hidden;
  position: relative;
}

.line-animation-panel2 {
  border-left: 1px solid rgb(255, 255, 255);
  border-right: 1px solid rgb(255, 255, 255);
}
.line-animation-panel3 {
  border-right: 1px solid rgb(255, 255, 255);
}

.line-animation-panel-content {
  background-color: rgb(255, 255, 255);
  width: 100%;
  height: 100%;
}

.line-animation-panel .line-animation-panel-content {
  transition: transform 1.5s ease;
}
.line-animation-panel2 .line-animation-panel-content {
  transition: transform 1.5s ease .05s;
}
.line-animation-panel3 .line-animation-panel-content {
  transition: transform 1.5s ease .1s;
}
.line-animation-panel4 .line-animation-panel-content {
  transition: transform 1.5s ease .15s;
}

.line-animation-panel-content.slide-out {
  transform: translateX(-101%);
}

.line-animation-columns-container.animated .line-animation-panel{
  border-color: transparent;
  transition: all 1s ease 0.75s;
}

.line-animation-content {
  position: relative;
  padding: 20px;
  z-index: 2;
  color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 1s ease 1.6s;
}

.line-animation-columns-container.animated .line-animation-content {
  opacity: 1;
}

.line-animation-title {
  font-size: 120px;
  font-weight: 500;
  margin-bottom: 1rem;
  line-height: 144px;
  font-family: "Museo Sans";
}

.line-animation-text {
  font-size: 30px;
  line-height: normal;
  font-family: "Museo Sans";
  padding-top: 100px;
  font-weight: 700;
}

.line-animation-title .highlighted{
  font-weight: 900;
  position: relative;
  display: inline;
}

/* .line-animation-columns-container.animated .line-animation-panel-wrapper{
  visibility: hidden;
  z-index: -1;
  transition: all 7s ease;
} */

.break-line{
  display: block;
}
.line-break-desktop, .line-break-laptop, .line-break-mobile {
  display: none;
}
@media (min-width: 1366px) {
  .line-break-laptop {
    display: inline;
  }
}
@media (min-width: 1024px) {
  .line-break-desktop {
    display: inline;
  }
}
@media screen and (max-width:767px) {
  .break-line{
    display: inline-block;
  }
  .line-break-mobile{
    display: inline;
  }
}

/* text animation */
.section{
   --highlight-base: rgb(0 0 0 / 40%);
   --highlight-base2: #BCBCBC;
    --highlight-base3: rgb(0 169 157 / 30%);
    --highlight-black: #000000;
    /* --highlight-black: rgb(0 0 0 / 60%); */
    --highlight-green:#00A99D;
}
/* .line.black {
    background-image: linear-gradient(90deg, var(--highlight-black) var(--highlight-offset), var(--highlight-base) var(--highlight-offset));
}
.line.green {
    background-image: linear-gradient(90deg, var(--highlight-green) var(--highlight-offset), var(--highlight-base) var(--highlight-offset));
} */
.char{
  color: var(--highlight-base);        
  transition: color 0.1s ease;
}
.word{
  color: var(--highlight-base);        
  transition: color 0.1s ease;
}
.line-row{
  display: block;
  color: var(--highlight-base2);        
  transition: color 0.1s ease;
}
.highlight-section .char, .highlight-section .line-row{
  color: var(--highlight-base3);    
}

.line-animation-content .line{
  background-image: none;
}

.highlight-section .line-animation-text {
  opacity: 0; /* Start invisible for animation */
}

#overview .row{
    height: 100%;
    align-items: center;
}
#overview .section-content p, #sustainable-review-content p{
    font-size: 35px;
    font-weight: 700;
    line-height: normal;
}

#financial-highlights{
  padding: 0 !important;
}
.line-animation-columns-container.highlight-section{
  height: auto;
}
.line-animation-columns-container.highlight-section .line-animation-section{
     height: auto;
     min-height: 600px;
}
.line-animation-columns-container.highlight-section .line-animation-panel-content{
  background-color: #00a99d;
}
.highlight-section.line-animation-columns-container.animated .line-animation-panel{
  border-color: rgb(0 0 0 / 0%);
  transition: all 1s ease 0.75s;
}

.highlight-section .line-animation-title {
  font-size: 100px;
  font-weight: 900;
  margin-bottom: 1rem;
  line-height: normal;
  font-family: "Museo Sans";
}

.highlight-section .line-animation-text {
  /* font-size: 30px; */
  font-size: 35px;
  line-height: normal;
  font-family: "Museo Sans";
  padding-top: 25px;
  /* font-weight: 500; */
  font-weight: 700;
}

@media screen and (max-width:1366px) {
  .line-animation-title {
    font-size: 100px;
    line-height: 130px;
  }
  #overview .section-content p,  #sustainable-review-content p{
    font-size: 30px;
  }
  .highlight-section .line-animation-title{
    font-size: 55px;
  }
  #home #non-financial-content-index{
    padding: 30px 0px 80px;
  }
}
@media screen and (max-width:1024px) {
  .line-animation-title {
    font-size: 55px;
    line-height: 65px;
  }
  .line-animation-text {
    font-size: 20px;
    padding-top: 50px;
  }
  #overview .section-content p, .highlight-section .line-animation-text,  #sustainable-review-content p{
    font-size: 20px;
  }
  .highlight-section .line-animation-title{
    font-size: 40px;
  }

  .line-animation-columns-container.highlight-section .line-animation-section{
      height: auto;
      min-height: auto;
  }
  .highlight-section .line-animation-content {
      padding: 80px 25px;
  }
}
@media screen and (max-width:767px) {
  #overview .section-content p, .highlight-section .line-animation-text,  #sustainable-review-content p{
    font-size: 16px;
    padding-top: 5px;
  }
  .highlight-section .line-animation-title {
      font-size: 36px;
  }
}

#energy-strategy-cards {
  position: relative;
  background: #f5f5f5;
  overflow: hidden;
}

#energy-strategy-cards .card-wrapper {
  position: relative;
  height: 100vh;
}

#energy-strategy-cards .card {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 1300px;
  height: 585px;
  border-radius: 30px;
  padding: 70px;
  text-align: center;
  color: white;
  backdrop-filter: blur(10px);
  /* box-shadow: 0 30px 60px rgba(0,0,0,0.3); */
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* opacity: 0; */
  border: 0 ;
}

#energy-strategy-cards .card::before {
  content: "";
  position: absolute;
  inset: 0;
  background-color:rgb(0 169 157 / 60%);
  z-index: 1;
  border-radius: 30px;
}

#energy-strategy-cards .card::after {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
  border-radius: 30px;
  opacity: var(--after-opacity, 0);
  transition: opacity 0.3s ease; /* optional if instant */
}

#energy-strategy-cards .card-content {
  --content-margin: auto;
  position: relative;
  z-index: 2;
  /* margin: var(--content-margin, auto);
  transition: margin 0.3s ease; */

  transform: translateY(0);
  transition: transform 0.3s ease;
}

#energy-strategy-cards .card h3 {
    font-weight: 700;
    margin-top: 18px;
    line-height: normal;
    font-size: 40px;
}

#energy-strategy-cards .card p {
  font-size: 20px;
  font-weight: 300;
  margin: 45px auto 0;
  opacity: 0.9;
  max-width: 900px;
  line-height: normal;
}

#energy-strategy-cards .card .card-desc{
  font-size: 24px;
  line-height: normal;
}

#energy-strategy-cards #card1 img{
    width: 76.5px;
    height: 76.5px;
}
#energy-strategy-cards #card2 img{
    width: 100px;
    height: auto;
}
#energy-strategy-cards #card3 img{
    width: 124px;
    height: auto;
}
#energy-strategy-cards .read-more-cta{
  margin: 50px auto 0;
  width: 0;
}
#energy-strategy-cards .card-link {
  display: inline-flex;
  align-items: center;
  gap: 25px;
  font-size: 18px;
  color: #ffffff;
  text-decoration: none;
  font-weight: 300;
  margin: 50px auto 0;
}

#energy-strategy-cards .card-link i {
  display: inline-block;
  width: 25px;
  height: 25px;
  background-image: url('../images/integrated-report-2024/arrow-right.svg');
  background-size: contain;
  background-repeat: no-repeat;
  transition: transform 0.3s ease;
  background-position: center;
}

#energy-strategy-cards .card-link:hover {
  opacity: 0.9;
   color: #ffffff;
}

#energy-strategy-cards .card-link:hover i {
  transform: translateX(4px);
}


/* Card backgrounds */
#card1 {
  background: url('../images/energy-transition-strategy/core-business.jpg') center/cover no-repeat;
  z-index: 10;
}

#card2 {
  background: url('../images/energy-transition-strategy/new-business.jpg') center/cover no-repeat;
  z-index: 11;
}

#card3 {
  background:
  linear-gradient(180deg, rgba(0, 169, 157, 0) 0%, rgba(0, 169, 157, 1) 100%),
  url('../images/energy-transition-strategy/net-zero-carbon-emissions.jpg') center/cover no-repeat;
  z-index: 12;
  
}

#energy-strategy-cards .card-stack {
  position: relative;
  width: 100%;
  height: 100%;
}

@media screen and (max-width:1366px) {
  #energy-strategy-cards .card{
    padding: 50px;
    /* height: 500px; */
  }
}
@media screen and (max-width:1024px) {
    #energy-strategy-cards .card .card-desc{
        font-size: 20px;
  }
  #energy-strategy-cards #card1 img{
    width: 60px;
    height: auto;
  }
  #energy-strategy-cards #card2 img{
      width: 60px;
      height: auto;
  }
  #energy-strategy-cards #card3 img{
      width: 60px;
      height: auto;
  }
    #energy-strategy-cards .card{
        padding: 20px;
        height: 450px;
  }
    #energy-strategy-cards .card p{
        font-size: 18px;
        margin: 20px auto 0;
  }
   #energy-strategy-cards .card h3{
      font-size: 28px;
      margin-top: 10px;
  }
  #energy-strategy-cards .card::before, #energy-strategy-cards .card, #energy-strategy-cards .card::after{
    border-radius: 15px;
  }
}
@media screen and (max-width:767px) {
   #energy-strategy-cards .card .card-desc{
        font-size: 16px;
  }
  #energy-strategy-cards .card p{
        font-size: 14px;
        margin: 20px auto 0;
  }
  #energy-strategy-cards .card-link{
    font-size: 16px;
    margin: 30px auto auto;
  }
  #energy-strategy-cards #card1 img{
    width: 50px;
    height: auto;
  }
  #energy-strategy-cards #card2 img{
      width: 50px;
      height: auto;
  }
  #energy-strategy-cards #card3 img{
      width: 50px;
      height: auto;
  }
}

/* file type accordion */
.file-type-accordion .accordion-body .ratio-4x3{
      --bs-aspect-ratio: 38%;
      /* background: #fff; */
      pointer-events: auto;
}
.file-type-accordion .accordion-item{
    border: none !important;
    overflow: hidden;
    background-image: url(../images/non-financial-content-index/accordion-file-top.png);
    padding-top: 6%;
    background-repeat: no-repeat;
    background-size: contain;    
    margin-top: -6%;
    z-index: 1;
    position: relative;
    pointer-events: none;
}

.file-type-accordion .accordion-button::after{
  display: none;
}

/* .file-type-accordion .accordion-header{
  background: #00A99D;
} */

.file-type-accordion .accordion-item .accordion-header .accordion-button{
    color: #ffffff !important;
    background: #00A99D;
    padding-bottom: 4% !important;
    padding-left: 20% !important;
    transition: all .3s ease;
    font-size: 30px;
    font-weight: 900;
    position: relative;
    z-index: 2;
    pointer-events: auto;
    line-height: normal;
}
.file-type-accordion .accordion-body:first-of-type{
    background: #00A99D;
    padding: 0px 50px 110px
}
.file-type-accordion .accordion-item .accordion-header .accordion-button.collapsed{
    padding-bottom: 5% !important;
    padding-top: 2%;
}
.file-type-accordion .accordion-item.last .accordion-header .accordion-button.collapsed{
      padding-bottom: 5%!important
}
.file-type-accordion .accordion-item:last-of-type{
  border-bottom-right-radius: 30px !important;
  border-bottom-left-radius: 30px !important;
}

.file-type-accordion .accordion-item:first-of-type {
  margin-top: 0;
}
.file-type-accordion .accordion-collapse{
    margin-top:-1px;
    z-index: 2;
    position: relative;
    transition: all .3s ease;
}
.non-financial-content-index-title .text-animation-rows{
  font-size: 25px;
}

#non-financial-content-index-title{
  min-height: auto !important;
}

/* .file-type-accordion .accordion-item .accordion-header .accordion-button[aria-expanded="true"] {
  padding-bottom: 2.2% !important;
}
.file-type-accordion .accordion-item .accordion-header .accordion-button.collapsed[aria-expanded="true"] {
  padding-bottom: 5% !important;
} */

#non-financial-content-index .accordion{
  opacity: 0;
  transform: translateY(50%);
}

@media screen and (min-width: 1900px) {
    .non-financial-content-index .container {
        max-width: 1638px;
    }
}

@media screen and (max-width: 1024px) {
    .file-type-accordion .accordion-item .accordion-header .accordion-button{
      font-size: 20px;
      min-height: 90px;
    }
    .file-type-accordion .accordion-body .ratio-4x3{
      --bs-aspect-ratio: 75%;
    }
    .file-type-accordion .accordion-body:first-of-type {
        padding: 0px 30px 70px;
    }
    /* .file-type-accordion .accordion-item .accordion-header .accordion-button[aria-expanded="true"] {
      padding-bottom: 2.8% !important;
    } */

}
@media screen and (max-width: 767px) {
    .file-type-accordion .accordion-body:first-of-type {
        padding: 0px 20px 50px;
    }
    /* .file-type-accordion .accordion-item .accordion-header .accordion-button[aria-expanded="true"] {
      padding-bottom: 6% !important;
    } */
    .file-type-accordion .accordion-item .accordion-header .accordion-button.collapsed {
        padding-bottom: 6% !important;
        padding-top: 4%;
    }
    .file-type-accordion .accordion-item.last .accordion-header .accordion-button.collapsed {
        padding-bottom: 6% !important;
    }
    .file-type-accordion iframe{
        border-radius: 8px !important;
    }
    .file-type-accordion .accordion-item .accordion-header .accordion-button{
      font-size: 16px;
    }
        
}

/*? \/ \/ \/ OLDER ONE CIRCLE THINGY \/ \/ \/*/

/* Creating Sustainable Value Section - Circular Diagram */
/* #creating-sustainable-value {
  padding: 80px 0 100px;
  position: relative;
  overflow: hidden !Important;
}
.sustainability-diagram {
  position: relative;
  width: 100%;
  margin: 0 auto;
}
.circle {
  border-radius: 50%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
  transition: transform 0.3s ease;
  position: relative;
}
.right-circle .circle, .left-circle .circle, .top-circle .circle  {
    width: 600px;
    height: 600px;
}
.center-circle .circle {
    width: 498px;
    height: 498px;
}
.center-circle .circle .circle-content{
    height: 385.578px;
    width: 385.578px;
    justify-content: center;
        margin-bottom: -1.2px;
}
.circle-content {
    padding: 0px;
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    height: 480px;
    width: 480px;
    border-radius: 50%;
    position: relative;

}

.circle h2 {
  font-size: 40px;
  line-height: normal;
  font-weight: 900;
  margin-bottom: 40px;
}

.circle-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 0px;
    margin-top: 15px;
}

.teal-bg {
  background-color: var(--background-theme-color);
}
.green-bg {
  background-color: #BFD730;
}
.yellow-bg {
  background-color: #FFBD27;
}
.blue-bg {
  background-color: #20419A;
}

#through-responsive-goovernance-circle{
    background: url(../images/creating-sustainable-value/through-responsive-goovernance.svg);
    background-repeat: no-repeat;
    background-size: 100%;
}
#delivering-net-zero-circle{
    background: url(../images/creating-sustainable-value/delivering-net-zero.svg);
    background-repeat: no-repeat;
    background-size: 100%;
} */
/* \/\/\/ Older Comment out \/\/\/ */
/* #thriving-with-nature-circle{
    background: url(../images/creating-sustainable-value/thriving-with-nature.svg);
    background-repeat: no-repeat;
    background-size: 100%;
} */
 /* /\/\/\ Older comment out /\/\/\ */
/* #fostering-a-just-transition-circle{
    background: url(../images/creating-sustainable-value/fostering-a-just-transition.svg);
    background-repeat: no-repeat;
    background-size: 100%;
}

#thriving-with-nature-circle::before{
  background: url(../images/creating-sustainable-value/thriving-with-nature-ring.svg);
  background-repeat: no-repeat;
  background-size: 100%;
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
#fostering-a-just-transition-circle::before{
  background: url(../images/creating-sustainable-value/fostering-a-just-transition-ring.svg);
  background-repeat: no-repeat;
  background-size: 100%;
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
#fostering-a-just-transition-circle .circle-content, #thriving-with-nature-circle .circle-content{
  box-shadow: 6px 0px 20px 0px #47474763 inset;
}


.circle-icons img{
  width: auto;
  height: 165px;
}
#thriving-with-nature-circle .circle-image img{
    position: absolute;
    width: 486px;
    height: auto;
    bottom: -7px;
    display: flex;
    right: -5px;
}
#fostering-a-just-transition-circle .circle-image img{
  position: absolute;
  width: 480px;
  height: auto;
  bottom: -18px;
  display: flex;
  right: -5px;
  z-index: 0;
}
#fostering-a-just-transition-circle .read-more-cta{
    margin: 100px 50px auto auto;
}
 #thriving-with-nature-circle .read-more-cta{
    margin: 100px auto auto 60px;
}
.circle-content::before{
  content: "";
  width:100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgb(31 31 31 / 71%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
  z-index: 2;
}
.circle-content:hover::before{
  opacity: 1;
}
.circle-content .read-more-cta{
  z-index: 3;
} */
/* Center circle */
/* .center-circle {
    top: 62%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 6 !important;
} */

/* Top circle */
/* .top-circle {
  left: 50%;
  transform: translateX(-50%);
  top: 5%;
  z-index: 4 !important;
} */

/* Left circle */
/* .left-circle {
  top: 69.5%;
  transform: translateY(-50%);
  left: 3.5%;
  z-index: 5 !important;
} */
 /* Right circle */
/* .right-circle {
    top: 69.5%;
    transform: translateY(-50%);
    right: 3.5%;
    z-index: 3 !important;
}
.circle-container {
  position: absolute;
  z-index: 2;
}

#creating-sustainable-value{
    min-height: auto !important;
    height: auto;
} */
/* #creating-sustainable-value .row{
  height: 100%;
}  */
/* .sustainability-diagram {
  min-height: 980px;
  max-width: 1250px;
}
#creating-sustainable-value .read-more-cta{
  max-width: 200px;
}
#creating-sustainable-value .read-more-cta svg{
    margin-right: 15px;
}
#creating-sustainable-value .read-more-cta:hover svg {
    transform: translateX(15px);
} */

/* Responsive styles for Creating Sustainable Value section */
/* @media screen and (max-width: 1280px) and (min-width: 1025px) {
  .right-circle .circle, .left-circle .circle, .top-circle .circle {
    width: 400px;
    height: 500px;
  }
  .center-circle .circle {
    width: 420px;
    height: 420px;
  }
  .center-circle .circle .circle-content {
    height: 325px;
    width: 325px;
  }
  .circle-content {
    height: 400px;
    width: 400px;
  }
  .circle h2 {
    font-size: 34px;
    margin-bottom: 30px;
  }
  .circle-icons img {
    width: 190px;
    height: auto;
  }
  #thriving-with-nature-circle .circle-image img {
    width: 400px;
  }
  #fostering-a-just-transition-circle .circle-image img {
    width: 370px;
  }
  .sustainability-diagram {
    min-height: 850px;
    max-width: 1100px;
  }
} */


/* @media screen and (max-width: 1280px) and (min-width: 768px) {
  .right-circle .circle, .left-circle .circle, .top-circle .circle {
    width: 380px;
    height: 380px;
  }
  .center-circle .circle {
    width: 320px;
    height: 320px;
  }
  .center-circle .circle .circle-content {
    height: 250px;
    width: 250px;
  }
  .circle-content {
    height: 300px;
    width: 300px;
  }
  .circle h2 {
    font-size: 26px;
    margin-bottom: 15px;
    line-height: 30px;
  }
  .circle-icons img {
    width: auto;
    height: 125px;
    margin-top: 15px;
    margin-bottom: 0px;
  }
  #thriving-with-nature-circle .circle-image img {
    width: 300px;
    right: -3px;
    bottom: -5px;
  }
  #fostering-a-just-transition-circle .circle-image img {
    width: 295px;
        right: -6px;
        bottom: -7px;
  }
  #fostering-a-just-transition-circle .read-more-cta {
    margin: 45px 65px auto auto;
    font-size: 16px;
  }
  #thriving-with-nature-circle .read-more-cta {
    margin: 45px auto auto 75px;
    font-size: 16px;
  }
  .sustainability-diagram {
    min-height: 650px;
    max-width: 900px;
  }
  #creating-sustainable-value .read-more-cta svg {
    margin-right: 10px;
    width: 22px;
  }
  #creating-sustainable-value .read-more-cta{
    max-width: 122px;
    min-width: 122px;
    font-size: 14px;
  }
  #creating-sustainable-value .read-more-cta svg{
      margin-right: 10px;
  }
  #creating-sustainable-value .read-more-cta:hover svg {
      transform: translateX(10px);
  }
  .read-more-cta span {
      font-size: 14px;
      gap:10px;
  }
}

@media screen and (max-width: 767px){
  .right-circle .circle, .left-circle .circle, .top-circle .circle {
    width: 202px;
    height: 202px;
  }
  .circle-content {
    width: 161px;
    height: 161px;
  }
  .center-circle .circle {
    width: 178px;
    height: 178px;
  }
  .center-circle .circle .circle-content {
      height: 125px;
      width: 125px;
  }

  .circle h2 {
    font-size: 16px;
    margin-bottom: 10px;
    line-height: normal;
  }
  .circle-icons img {
    width: auto;
    height: 65px;
    margin-bottom: 0px;
    margin-top: 5px;
  }
  #thriving-with-nature-circle .circle-image img {
        width: 162.09px;
        right: -2px;
        bottom: -2px;
  }
  #fostering-a-just-transition-circle .circle-image img {
        width: 140.02px;
        right: 2px;
        bottom: -3px;
  }
  #fostering-a-just-transition-circle .read-more-cta {
     margin:27px 15px auto auto
  }
  #thriving-with-nature-circle .read-more-cta {
    margin: 15px auto auto 36px
  }
  #delivering-net-zero-circle .read-more-cta {
    margin: 0 0 0 0px;
  }
  .sustainability-diagram {
    min-height: 450px;
    max-width: 375px;
  }
  #creating-sustainable-value .read-more-cta svg {
    margin-right: 10px;
    width: 10px;
  }
  #creating-sustainable-value .read-more-cta{
    max-width: 70px;
    min-width: 70px;
    font-size: 10px;
  }
  #creating-sustainable-value .read-more-cta svg{
      margin-right: 5px;
  }
  #creating-sustainable-value .read-more-cta:hover svg {
      transform: translateX(5px);
  }
  .sustainability-diagram .read-more-cta span {
      font-size: 10px;
      gap:0px;
      text-align: left;
      line-height: normal;
  }
  .sustainability-diagram .read-more-cta .cta-line{
    margin-top: 2px;
  }
  #creating-sustainable-value {
      padding: 30px 0;
  }
} */
/*? /\ /\ /\ OLDER CIRLCE THINGY /\ /\ /\  */

/*? \/ \/ \/ NEW CIRCLY THINGY \/ \/ \/  */
  section#creating-sustainable-value {
      margin: 0;
      background: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 110vh !important;
    }

    section#creating-sustainable-value .container {
      position: relative;
      width: 100vw;
      max-width: 600px;
      height: 100vw;
      max-height: 600px;
    }

    section#creating-sustainable-value .circle {
      position: absolute;
      width: 400px;
      height: 400px;
      border-radius: 50%;
    }

    /* Green (left) - on top of blue */
    section#creating-sustainable-value .green {
      /* top: 22%;
      left: 22%; */
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%) scale(0.5);
      background: #43aa8b;
      z-index: 60;
    }

    /* Blue (top) - on top of yellow */
    section#creating-sustainable-value .blue {
      /* top: 22%;
      left: 22%; */
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%) scale(0.5);
      background: #277da1;
      z-index: 40;
    }

    /* Yellow (right) - split to allow bottom part to be on top of green */
    section#creating-sustainable-value .yellow-top {
      background: #fcbf49;
      /* clip-path: inset(0 0 43% 0); */
      clip-path: inset(0 0 0 0);
      z-index: 20;
      border-radius: 50%;
      position: absolute;
    }

    section#creating-sustainable-value .yellow-bottom {
      background: #fcbf49;
      /* clip-path: inset(57% 0 0 0); */
      clip-path: inset(0 0 0 0);
      z-index: 80;
      border-radius: 50%;
      position: absolute;
    }

    section#creating-sustainable-value .center-circle {
      position: absolute;
      width: 250px;
      height: 250px;
      top: 50%;
      left: 50%;
      background: #6f42c1;
      border-radius: 50%;
      z-index: 100;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      text-align: center;
      font-size: 14px;
      padding: 1rem;
      transform: translate(-50%, -50%) scale(1);
    }

    section#creating-sustainable-value .circle.blue {
      background-image: url('../images/creating-sustainable-value/dnz.png');
      background-size: cover;
      background-position: center;
    }

    section#creating-sustainable-value .circle.green {
      background-image: url('../images/creating-sustainable-value/twn.png');
      background-size: cover;
      background-position: center;
    }

    section#creating-sustainable-value .yellow-top,
    section#creating-sustainable-value .yellow-bottom {
      width: 400px;
      height: 400px;
      border-radius: 50%;
      background-image: url('../images/creating-sustainable-value/fjt.png');
      background-size: cover;
      background-position: center;
      position: absolute;
      /* top: 22%;
      left: 22%; */
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%) scale(0.5);
    }

    section#creating-sustainable-value .center-circle {
      background-image: url('../images/creating-sustainable-value/main-circle.png');
      background-size: cover;
      background-position: center;
    }

    section#creating-sustainable-value .cta {
      min-width: 115px;
      border-bottom: 1px solid black;
      padding-bottom: 5px;
      position: absolute;
      color: #000;
      font-weight: 500;
      font-size: 16px;
      font-family: Arial, sans-serif;
      display: inline-flex;
      justify-content: space-between;
      align-items: center;
      z-index: 300;
      text-decoration: none;
      opacity: 0;
      transform: translate(-50%, 50%);
    }
    section#creating-sustainable-value .cta>.cta-text{
      margin-right: 10px;
    }
    
    .cta.animated {
      animation: 0.5s fade_in 0.6s ease forwards;
    }

    @keyframes fade_in {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    /* .cta:hover::after {
      transform: translateX(4px);
    } */

    section#creating-sustainable-value .top,
    section#creating-sustainable-value .right,
    section#creating-sustainable-value .bottom,
    section#creating-sustainable-value .left {
      position: absolute;
    }

    section#creating-sustainable-value .top {
      top: -15%;
      left: 50%;
      transform: translate(-50%, 50%);
    }

    section#creating-sustainable-value .right {
      top: 50%;
      /* right: -35%; */
      right: -55%;
      transform: translate(-50%, 50%);
    }

    section#creating-sustainable-value .bottom {
      bottom: -2%;
      left: 50%;
      transform: translate(-50%, 50%);
    }

    section#creating-sustainable-value .left {
      top: 50%;
      /* left: -15%; */
      left: -25%;
      transform: translate(-50%, 50%);
    }

    section#creating-sustainable-value .blue.animated {
      animation: 0.6s blue_anim ease forwards;
    }

    section#creating-sustainable-value .yellow-bottom.animated {
      animation: 0.6s bottom_yellow_anim ease forwards;
    }

    section#creating-sustainable-value .yellow-top.animated {
      animation: 0.6s top_yellow_anim ease forwards;
    }

    section#creating-sustainable-value .green.animated {
      animation: 0.6s green_anim ease forwards;
    }

    @keyframes top_yellow_anim {
      0% {
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%) scale(0.5);
        /* clip-path: inset(0 0 0 0); */
      }

      80% {
        /* clip-path: inset(0 0 0 0); */
        clip-path: inset(0 0 43% 0);
      }

      100% {
        transform: translate(-50%,-50%) scale(1);
        left: 68%;
        top: 58%;
        clip-path: inset(0 0 43% 0);
      }
    }

    @keyframes bottom_yellow_anim {
      0% {
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%) scale(0.5);
        /* clip-path: inset(0% 0 0 0); */
      }

      80% {
        /* clip-path: inset(0% 0 0 0); */
        clip-path: inset(57% 0 0 0);
      }

      100% {
        transform: translate(-50%,-50%) scale(1);
        left: 68%;
        top: 58%;
        clip-path: inset(57% 0 0 0);
      }
    }

    @keyframes blue_anim {
      0% {
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%) scale(0.5);

      }

      100% {
        transform: translate(-50%,-50%) scale(1);
        top: 32%;
        left: 45%;
      }

    }

    @keyframes green_anim {
      0% {
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%) scale(0.5);
      }

      100% {
        transform: translate(-50%,-50%) scale(1);
        top: 58%;
        left: 33%;
      }

    }

    @keyframes center_anim {
      0% {
        transform: translate(-50%,-50%) scale(1.5);
        top: 10%;
      }

      100% {
        transform:translate(-50%,-50%) scale(1);
        top: 22%;
        left: 22%;
      }
    }

    @media screen and (max-width: 576px) {
      section#creating-sustainable-value {
        margin: 0;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
      }
      section#creating-sustainable-value .cta {
        font-size: 12px;
        min-width: 85px;
      }

      section#creating-sustainable-value .circle,
      section#creating-sustainable-value .yellow-top,
      section#creating-sustainable-value .yellow-bottom {
        width: 50vw;
        height: 50vw;
      }

      section#creating-sustainable-value .yellow-bottom,
      section#creating-sustainable-value .yellow-top,
      section#creating-sustainable-value .green,
      section#creating-sustainable-value .blue {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.5);
      }

      section#creating-sustainable-value .top, 
      section#creating-sustainable-value .bottom,
      section#creating-sustainable-value .left,
      section#creating-sustainable-value .right {
        min-width: 100px;
      }

      section#creating-sustainable-value a span svg {
        margin-right: 15px;
      }

      .section#creating-sustainable-value a:hover svg {
        transform: translateX(15px);
      }

      section#creating-sustainable-value .center-circle {
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%);
        
      }

      section#creating-sustainable-value .center-circle {
        width: 30vw;
        height: 30vw;
      }

      section#creating-sustainable-value .top {
        top: -4%;
        left: 50%;
      }

      section#creating-sustainable-value .bottom {
        bottom: -5%;
        left: 50%;
      }

      section#creating-sustainable-value .left {
        top: 84%;
        left: 30%;
      }

      section#creating-sustainable-value .right {
        top: 84%;
        right: -5%;
      }

      @keyframes top_yellow_anim {
        0% {
          transform: scale(0.5);
          /* clip-path: inset(0 0 0 0); */
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
          clip-path: inset(0 0 43% 0);
        }

        90% {
          /* clip-path: inset(0 0 0 0); */
          clip-path: inset(0 0 43% 0);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          left: 63%;
          top: 55%;
          clip-path: inset(0 0 43% 0);
        }
      }

      @keyframes bottom_yellow_anim {
        0% {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
          clip-path: inset(57% 0 0 0);
        }

        90% {
          /* clip-path: inset(0% 0 0 0); */
          clip-path: inset(57% 0 0 0);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          left: 63%;
          top: 55%;
          clip-path: inset(57% 0 0 0);
        }
      }

      @keyframes blue_anim {
        0% {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);

        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          top: 36%;
          left: 50%;
        }

      }

      @keyframes green_anim {
        0% {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          top: 55%;
          left: 35%;
        }

      }

      @keyframes center_anim {
        0% {
          transform:translate(-50%,-50%) scale(1);
          top: 50% !important;
          left: 50% !important;
        }

        100% {
          transform: scale(1);
          top: 31%;
          left: 31%;
        }
      }
    }

    @media screen and (min-width:577px) and (max-width: 768px) {
      section#creating-sustainable-value .cta {
        font-size: 12px;
        min-width: 85px;
      }

      section#creating-sustainable-value .circle,
      section#creating-sustainable-value .yellow-top,
      section#creating-sustainable-value .yellow-bottom {
        width: 50vw;
        height: 50vw;
      }

      section#creating-sustainable-value .yellow-bottom,
      section#creating-sustainable-value .yellow-top,
      section#creating-sustainable-value .green,
      section#creating-sustainable-value .blue {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.5);
      }

      section#creating-sustainable-value .center-circle {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      section#creating-sustainable-value .center-circle {
        width: 30vw;
        height: 30vw;
      }



      section#creating-sustainable-value .top, 
      section#creating-sustainable-value .bottom,
      section#creating-sustainable-value .left,
      section#creating-sustainable-value .right {
        min-width: 100px;
      }

      section#creating-sustainable-value a span svg {
        margin-right: 15px;
      }

      .section#creating-sustainable-value a:hover svg {
        transform: translateX(15px);
      }

      section#creating-sustainable-value .top {
        top: -4%;
        left: 49%;
      }

      section#creating-sustainable-value .bottom {
        bottom: 2%;
        left: 49%;
      }

      section#creating-sustainable-value .left {
        top: 84%;
        left: 30%;
      }

      section#creating-sustainable-value .right {
        top: 84%;
        right: -5%;
      }

      @keyframes top_yellow_anim {
        0% {
          transform: scale(0.5);
          /* clip-path: inset(0 0 0 0); */
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
          clip-path: inset(0 0 43% 0);
        }

        90% {
          /* clip-path: inset(0 0 0 0); */
          clip-path: inset(0 0 43% 0);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          left: 65%;
          top: 55%;
          clip-path: inset(0 0 43% 0);
        }
      }

      @keyframes bottom_yellow_anim {
        0% {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
          clip-path: inset(57% 0 0 0);
        }

        90% {
          /* clip-path: inset(0% 0 0 0); */
          clip-path: inset(57% 0 0 0);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          left: 65%;
          top: 55%;
          clip-path: inset(57% 0 0 0);
        }
      }

      @keyframes blue_anim {
        0% {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          top: 36%;
          left: 47%;
        }

      }

      @keyframes green_anim {
        0% {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          top: 55%;
          left: 35%;
        }

      }

      @keyframes center_anim {
        0% {
          transform: scale(1);
          top: 24%;
          left: 30%;
        }

        100% {
          transform: scale(1);
          top: 31%;
          left: 31%;
        }
      }
    }

    @media screen and (min-width: 769px) and (max-width: 992px) {
      section#creating-sustainable-value .cta {
        font-size: 14px;
        min-width: 85px;
      }

      section#creating-sustainable-value .circle,
      section#creating-sustainable-value .yellow-top,
      section#creating-sustainable-value .yellow-bottom {
        width: 50vw;
        height: 50vw;
      }

      section#creating-sustainable-value .yellow-bottom,
      section#creating-sustainable-value .yellow-top,
      section#creating-sustainable-value .green,
      section#creating-sustainable-value .blue {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.5);
      }

      section#creating-sustainable-value .center-circle {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      section#creating-sustainable-value .center-circle {
        width: 30vw;
        height: 30vw;
      }

      section#creating-sustainable-value .top {
        top: -15%;
        left: 49%;
      }

      section#creating-sustainable-value .bottom {
        bottom: -14%;
        left: 49%;
      }

      section#creating-sustainable-value .left {
        top: 95%;
        left: 28%;
      }

      section#creating-sustainable-value .right {
        top: 95%;
        right: 15%;
      }

      @keyframes top_yellow_anim {
        0% {
          transform: scale(0.5);
          /* clip-path: inset(0 0 0 0); */
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
          clip-path: inset(0 0 50% 0);
        }

        90% {
          /* clip-path: inset(0 0 0 0); */
          clip-path: inset(0 0 50% 0);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          left: 70%;
          top: 57%;
          clip-path: inset(0 0 50% 0);
        }
      }

      @keyframes bottom_yellow_anim {
        0% {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
          clip-path: inset(50% 0 0 0);
        }

        90% {
          /* clip-path: inset(0% 0 0 0); */
          clip-path: inset(50% 0 0 0);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          left: 70%;
          top: 57%;
          clip-path: inset(50% 0 0 0);
        }
      }

      @keyframes blue_anim {
        0% {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);

        }


        100% {
          transform: translate(-50%, -50%) scale(1);
          top: 32%;
          left: 40%;
        }

      }

      @keyframes green_anim {
        0% {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) scale(0.5);
        }

        100% {
          transform: translate(-50%, -50%) scale(1);
          top: 57%;
          left: 31%;
        }

      }

      @keyframes center_anim {
        0% {
          transform: scale(1);
          top: 24%;
          left: 30%;
        }

        100% {
          transform: scale(1);
          top: 31%;
          left: 31%;
        }
      }
    }

    @media screen and (min-width: 993px) and (max-width: 1200px) {
      section#creating-sustainable-value .right {
        top: 55%;
        right: -38%;
      }

      section#creating-sustainable-value .bottom {
        bottom: -8%;
        left: 55%;
      }

      section#creating-sustainable-value .left {
        top: 55%;
        left: -12%;
      }
    }

    @media screen and (min-width: 1201px) {}

    @media screen and (min-width: 1400px) {}
/*? /\ /\ /\ NEW CIRCLY THINGY /\ /\ /\  */
.section-height-auto{
    height: auto;
    min-height: auto !important;
}
#sustainable-review-content > .container{
    padding: 100px 25px;
    padding-bottom: 0px; 
}
#sustainable-review-content .line-row{
    color: var(--highlight-base3);
}
#sustainable-review-content .line{
  background-image: none;
}
@media screen and (max-width: 767px) {
  #sustainable-review-content > .container {
    padding: 100px 25px;
  }
  #sustainable-review-content .pt-5{
        padding-top: 0px!important;
  }
  #sustainable-review-content .col-lg-8{
    justify-content: center !important;
    gap: 30px 50px;
  }
  #sustainable-review-content > .container {
      padding: 50px 25px;
  }
}

/* finanacial section */
#financial{
      background: #EEEEEE;
}
.financial-container {
  display: flex;
  gap: 80px;
  align-items: flex-start;
  /* min-height: 100vh; */
  min-height: 70vh;
  padding: 100px 25px;
}

.financial-container.full-panel-title {
  display: flex;
  gap: unset;
  align-items: flex-start;
  min-height: unset;
  /* padding: 100px 25px; */
  padding: 150px 25px;
  padding-bottom: unset;
}

.full-panel {
  width: 100%;
}

.left-panel {
  width: 35%;
}

.full-panel h2 {
    font-size: 34px;
    font-weight: 700;
    /* margin-bottom: 80px; */
    line-height: normal;
    opacity: 0;
    transform: translateX(-50%);
}

.left-panel h2 {
    font-size: 34px;
    font-weight: 700;
    margin-bottom: 80px;
    line-height: normal;
    opacity: 0;
    transform: translateX(-50%);
}

.left-panel .financial-intro {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 40px;
  color: #333;
  opacity: 0;
  transform: translateX(-50%);
}

.tabs-item {
  display: flex;
  align-items: center;
  gap: 25px;
  cursor: pointer;
  background: transparent;
  border: none;
  width: 90%;
  color: #000;
  padding: 0;
  position: relative;
  z-index: 1;
  max-width: 500px;
  height: 80px;
  font-size: 18px;
  text-align: left;
  line-height: normal;
}

.tab-icon-circle {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
}

.tabs-item::before{
    content: "";
    position: absolute;
    width: 80px;
    height: 100%;
    background: #00a79d;
    border-radius: 50px;
    transition: all 0.6s linear;
    z-index: -1;
}

.tabs-item.active::before{
    background: linear-gradient(to right, #00a79d, #7b3f98);
    width: 100%;
  }

.tabs-item.active {
  color: #fff;
  font-size: 20px;
}

.tabs-item.active .tab-icon-circle {
  background: transparent;
  color: white;
}

.right-panel {
  width: 65%;
  max-width: 800px;
}

.right-panel p {
    margin-bottom: 20px;
    line-height: 1.6;
    font-size: 18px;
    line-height: normal;
    font-weight: 300;
}
.right-panel .financial-intro{
    opacity: 0;
    transform: translateX(50%);
}

.right-panel p#description{
  font-weight: 700;
  padding-top: 30px;
  opacity: 0;
  transform: translateX(-50%);
}

.financial-bars {
  /* margin-top: 150px; */
  margin-top: 100px;
  width: 100%;
  opacity: 0;
  transform: translateX(50%);
}

.financial-bar-row {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  gap: 16px;
  height: 60px;
  width: 100%;
  gap: 30px;
}

.financial-year {
  width: 60px;
  font-weight: bold;
  flex-shrink: 0;
  font-size: 26px;
  font-family: "Museo Sans", sans-serif;
}

.financial-bar {
  height: 60px;
  flex-grow: 1;
  position: relative;
  width: calc(100% - 160px); /* Account for year and label width */
  max-width: 600px;
}

.financial-bar-inner {
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  transform-origin: left;
  transform: scaleX(0);
  transition: transform 0.5s ease-out; /* Changed from 1.5s to 0.8s for faster animation */
  width: 100%;
   font-size: 20px;
}

.financial-bar-label {
  width: 115px;
  text-align: right;
  font-weight: 700;
  margin-right: 0;
  margin-left: auto;
  flex-shrink: 0;
  font-family: "Museo Sans", sans-serif;
  font-size: 24px;
  line-height: normal;
}

.tab-buttons {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
  gap: 20px;
  opacity: 0;
  transform: translateY(50%);
}

.financial-container .tab-icon-circle i{
    width: 60px; 
    height: 24px; 
    background-image: url(../images/financial-performance/plus-icon.svg);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  }
  .financial-container .tabs-item.active .tab-icon-circle i{
      width: 65px; 
      height: 40px;
      /* margin-left: 10px; */
  }

  .tabs-item[data-key="revenue"].active .tab-icon-circle i{
    background-image: url(../images/integrated-report-2024/revenue-icon.gif);
  }
  .tabs-item[data-key="pat"].active .tab-icon-circle i{
    background-image: url(../images/integrated-report-2024/pat-icon.gif);
  }
  .tabs-item[data-key="totalassets"].active .tab-icon-circle i{
    background-image: url(../images/integrated-report-2024/total-assets-icon.gif);
  }
  .tabs-item[data-key="cffo"].active .tab-icon-circle i{
    background-image: url(../images/integrated-report-2024/cffo-icon.gif);
  } 
  
  .financial-container svg{
        width: 100%;
  }

@media (max-width: 1440px) {
   .financial-bar-label {
    font-size: 18px;
    }
}
@media (max-width: 1366px) {
     .financial-bar-label {
    font-size: 16px;
    }
}
@media (max-width: 1200px) {
  .financial-container {
    gap: 40px;
  }
  
  .financial-bars {
    margin-top: 100px;
  }

  .full-panel {
    width: 100%;
  }

  .left-panel {
    width: 100%;
  }
  .right-panel{
    width: 60%;
  }
  .tabs-item{
      font-size: 18px;
      height: 65px;
  }
  .tabs-item.active{
    font-size:20px;
  }
  .tab-icon-circle {
    width: 65px;
    height: 65px;
  }
  .tabs-item::before{
    width: 65px;
  }
  .right-panel p{
    font-size: 16px;
  }
 
}

@media (max-width: 1024px) {
  .financial-container {
    flex-direction: column;
  }
  .full-panel .left-panel, .right-panel {
    width: 100%;
    max-width: 100%;
  }
  .financial-bars {
    margin-top: 0px;
  }
  .tab-buttons{
    margin-bottom: 10px;
  }
  .tabs-item {
     font-size: 16px;
      gap: 15px;
  }
  .tabs-item.active {
    font-size: 18px;
  }
  .financial-year {
  font-size: 20px;
  }
  .financial-bar-label {
    font-size: 16px;
  }
  .full-panel h2{
        margin-bottom: 30px;
  }
  .left-panel h2{
        margin-bottom: 30px;
  }
}

@media (max-width: 768px) {
  .financial-container {
    /* padding: 80px 25px; */
    padding: 50px 25px;
  }
  
  .financial-bar-row {
    height: auto;
    margin-bottom: 10px;
    gap: 5px;
  }
  
  .financial-bar {
    height: auto;
  }
  
  .financial-bar-inner {
    height: auto;
  }
  
  .financial-year {
    width: 50px;
  }
  
  .financial-bar-label {
    width: auto;
    font-size: 14px;
    max-width: 45px;
  }
  .full-panel h2 {
    font-size: 20px;
  }
  .left-panel h2 {
    font-size: 20px;
  }
  .right-panel p{
    font-size: 16px;
  }
  .right-panel p#description{
    padding-top: 20px;
  }
    .financial-year {
  font-size: 18px;
  }
  .financial-bar-label {
    font-size: 14px;
  }
  .financial-container .tab-icon-circle i {
        width: 50px;
        height: 24px;
  }
  .financial-container .tabs-item.active .tab-icon-circle i{
        width: 60px;
        height: 30px;
  }
}

/* audited financial statements */
.financial-statement-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100vh;
  position: relative;
  padding: 0 0% 0 12%;
  overflow: hidden;
  background: #fff;
}

.financial-statement-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(../images/integrated-report-2024/pir-statement-bg-2.jpg) no-repeat;
  background-size: cover;
  z-index: 0;
}

.financial-statement-left {
  flex: 1;
  max-width: 600px;
  margin-left: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 1;
}

.financial-statement-text {
  font-size: 2.8rem;
  font-weight: 800;
  color: #000;
  line-height: 1.3;
}

.financial-statement-right {
  flex: 1;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.financial-statement-book-img {
    width: 100%;
    max-width: 930px;
}

.financial-statement-text-group {
  display: flex;
  flex-direction: column;
  gap: 0.4em; /* spacing between lines */
  max-width: 90vw; /* limit width on small screens */
  position: absolute;
  transform: translate(50%, 0px);
  top: 42%;
}

.financial-statement-text {
    font-size: 45px;
    font-weight: 900;
    line-height: normal;
    color: #00A19C;
    text-align: left;
    font-family: "Museo Sans", sans-serif;
}

.financial-statement-text .line-row{
    color: #00A19C;
    transform: translateY(30px);
    opacity: 0;
}
.financial-statement-section .financial-statement-background{
  opacity: 0;
  transform-origin: left;
  transform: scale3d(1, 1, 1);
}
.financial-statement-section .read-more-cta{
  margin: 50px auto auto 0;
}
.financial-statement-section .financial-statement-right{
  opacity: 0;
  transform: translateX(50%);
}
.financial-statement-section .read-more-cta{
  opacity: 0;
  transform: translateX(-50%);
}

@media (max-width: 1024px) {
  .financial-statement-section {
    flex-direction: column-reverse;
    padding: 40px 20px;
    height: auto;
   text-align: left;
  }

  .financial-statement-text-group {
    position: relative;
    transform: translate(0%, 0px);
  }

  .financial-statements-background {
    background-size: contain;
  }

  .financial-statement-left {
    margin-left: 0;
    padding: 40px 20px;
    align-items: center;
    margin-top: -60px;
  }

  .financial-statement-text {
    font-size: 2rem;
  }

  .financial-statement-right {
    padding: 20px;
  }

  .financial-statement-book-img {
    width: 100%;
    max-width: 650px;
  }

  .financial-statement-text-group {
    max-width: 100%;
    padding: 0;
    align-items: center; /* center text on small screens */
    text-align: left;
  }

  .financial-statement-text {
    font-size: 30px;
    text-align: left;
  }

  .financial-statement-background{
    background-size: 160%;
  }
}



/* key messages */
#home #key-messages{
    padding: 0px 0px 0px 0px;
}
#home #key-messages .key-message-content {
    background: #F5F5F5;
    min-height: 100vh;
    display: flex;
    align-items: flex-end;
}
#home #key-messages .quote-wrapper img{
  width: 100%;
}
#home #key-messages .quote-message{
    max-width: 800px;
    min-width: 800px;
}
.quote-message p{
      margin-top: 90px;
}
#home #key-messages .quote-content .quote-content-wrap h6.w-900{
      font-size: 20px;
}
#home #key-messages .quote-img {
  width: 90px;
  height: 90px;
}
h6.position{
    font-size: 16px;
    font-weight: 300;
}
.quote-message p{
    font-size: 24px;
    line-height: normal;
    font-weight: 500;
}
.quote-content .read-more-cta{
    margin-top: 100px;
}
.quote-content .subtitle{
    font-size: 16px !important;
    font-family: "Museo Sans";
        line-height: normal;
}
.quote-wrapper .bgelements{
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
    background-image: url(../images/key-messages/key-messages-img-bg-2.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: bottom;
    opacity: 0;
}
.key-message-content .read-more-cta{
  opacity: 0;
  transform: translateX(-50%);
}
.show-before.quote-wrapper:before{
  opacity: 1;
}

@media (max-width: 1900px) {
    #home #key-messages .quote-message{
        max-width: 600px;
      min-width: 600px;
    }
}
@media (max-width: 1600px) {
  #home #key-messages .quote-message{
      max-width: 500px;
    min-width: 500px;
  }
  .quote-message p{
      font-size: 20px;
          margin-top: 60px;
  }
  #home #key-messages .quote-img {
    width: 60px;
    height: 60px;
  }
  .quote-content .read-more-cta {
    margin-top: 60px;
  }
}
@media (max-width: 1366px) {
    #home #key-messages .key-message-content {
      min-height: auto;
    }
    #home #key-messages .quote-message {
      max-width: 100%;
      min-width: 100%;
    }
    .quote-message p {
      font-size: 14px;
      margin-top: 40px;
    }

    .quote-content .subtitle{
        font-size: 12px !important;
    }

    #home #key-messages .quote-img {
      width: 40px;
      height: 40px;
    }
    .quote-content .read-more-cta {
      margin-top: 40px;
    }
    #home #key-messages .quote-img-close{
      left: 90%;
    }
}
@media (max-width: 767px) {
    #home #key-messages .quote-img-close{
          left: 90%;
    }
    .quote-wrapper::after{
        content: "";
        height: 2px;
        width: 90%;
        background: #000;
        position: relative;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        display: block;
    }
    .key-message-content .mobile-image{
        width: 90%;
        margin: auto;
    }
    #home #key-messages .quote-img {
        width: 33px;
        height: 30px;
    }
    #home #key-messages .quote-img-open {
        margin-right: 0px;
    }
    .key-message-content.image-right .row{
      flex-direction: column-reverse;
    }
}

/* energy transition strategy */
.energy-transition-strategy .container {
    margin: auto;
    padding: 100px 25px;
}

.energy-transition-strategy h2 {
    font-size: 50px;
    color: #00a19c;
    font-weight: 900;
    margin-bottom: 50px;
    text-align: center;
    line-height: normal;
    opacity: 0;
}

.energy-transition-strategy .ets-card {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    opacity: 0;
    transform: translateY(150px); 
}

.energy-transition-strategy .ets-card img {
    width: 100%;
    height: auto;
    display: block;
}

.energy-transition-strategy .ets-card-content {
    position: absolute;
    bottom: 0px;
    /* left: 50%; */
    /* transform: translateX(0%) translateY(-35%); */
    color: white;
    padding: 40px 70px;
    z-index: 2;
    /* transition: transform 0.5s ease-in-out; */
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    opacity: 0;
    transform: translateY(-50px); 
}

.energy-transition-strategy .ets-card-content p {
    margin: 0 0 40px;
    font-size: 24px;
    line-height: normal;
    font-weight: 300;
}
.ets-card::before{
    content: "";
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    background: linear-gradient(to top, rgb(0, 0, 0), rgba(0, 0, 0, 0) 50%);
    z-index: 1;
    border-radius: 20px;
    opacity: 1;
}

#downloads .energy-transition-strategy .container {
    margin: auto;
    padding: 0px;
    padding-top: 100px;
}

#downloads .energy-transition-strategy h2 {
    margin-bottom: 0px;
}

#downloads .download-button-container {
   padding-top: 50px;
   padding-bottom: 100px;
}

.download-button-container a.download-card {
    /* height: 411px; */
    height: 380px;
    /* display: flex; */
    display: block;
    justify-content: center;
    width: auto;
    /* width: 20%;
    width: calc(20% - 20px); */
}

.download-button-container a:hover img {
    transform: translateY(-5px);
}

.download-button-container a:hover .text i:before {
    transform: translateX(-5px);
}

.download-item {
    opacity: 1;
    transition: opacity 0.5s ease, transform 0.3s ease;
    text-align: center;
    position: relative;
    /* flex: 0 0 210px; */
    cursor: pointer;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    /* background: #00A99D; */
    background: linear-gradient(to top, #763F98, #00A99D);
    padding: 20px;
    overflow: hidden;
    height: 100%;
    /* margin: 0 15px; */
    /* height: 300px; */
}

.download-item.active {
    opacity: 0;
    transition: opacity 0.5s ease, transform 0.3s ease;
    transition-delay: 0.5s;
}

.download-item img {
    width: auto;
    /* height: 200px; */
    /* height: 345px; */
    height: 280px;
    object-fit: contain;
    border-radius: 10px;
    margin-bottom: 0px;
    transition: transform 0.3s ease;
    /* max-height: 200px; */
     object-fit: contain;
     object-position: center;
}

.download-item .text {
    color: #fff;
    font-size: 16px;
    line-height: normal;
    font-weight: 600;
    text-align: left;
    bottom: 10px;
    position: absolute;
    /* padding: 20px; */
    padding: 10px;
    padding-right: 20px;
    padding-left: 0px;
    /* width: 100%; */
    display: flex;
    align-items: flex-end;
    gap: 9px;
}

.download-item .text i:before {
    content: "";
    position: relative;
    display: block;
    right: 0;
    /* top: 50%; */
    /* transform: translateY(-50%); */
    width: 20px;
    height: 20px;
    background: url('./../../assets/images/integrated-report-2024/arrow-right.svg') no-repeat center center;
    background-size: contain;
    transition: transform 0.3s ease;
}


/* Hidden state before scroll trigger */
#downloads .download-button-container a {
  opacity: 0;
  transform: translateY(20px);
}

/* Active state triggers animation */
/* #downloads.active h2 {
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 0.2s;
} */

#downloads.active .download-button-container a:nth-child(1) {
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 1.0s;
}

#downloads.active .download-button-container a:nth-child(2) {
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 1.8s;
}

#downloads.active .download-button-container a:nth-child(3) {
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 2.6s;
}

#downloads.active .download-button-container a:nth-child(4) {
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 3.4s;
}

#downloads.active .download-button-container a:nth-child(5) {
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 4.2s;
}

/* Fade-in keyframes */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}


@media screen and (max-width: 1024px) {
  .energy-transition-strategy .ets-card-content p{
    font-size: 18px;
  }
}
@media screen and (max-width: 768px) {
    .energy-transition-strategy h1 {
        font-size: 1.5rem;
        text-align: center;
    }

    .energy-transition-strategy .ets-card-content {
        bottom: 50px;
        padding: 20px;
        bottom: 20px;
    }

    .energy-transition-strategy .ets-card {
        margin-bottom: 20px;
    }

    .energy-transition-strategy .ets-card img {
        height: 650px;
        object-fit: cover;
    }
    .energy-transition-strategy .ets-card-content p {
      font-size: 16px;
    }
    .energy-transition-strategy h2 {
    font-size: 30px;
  }

  #downloads .energy-transition-strategy h2 {
    opacity: 1;
  }

  #downloads .download-button-container a {
    opacity: 1;
    transform: translateY(0px);
  }

  .download-button-container a.download-card {
    /* height: 290px; */
    height: 250px;
  }

  #downloads .download-button-container {
    background-position: center;
  }

  #downloads.active .download-button-container a {
    animation: none!important;
  }

  #downloads .download-button-container {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

/* leaders modal  */
.leaders-modal h3{
    font-size: 18px;
}
.leaders-modal .modal-title{
  font-size: 20px;
  padding-bottom: 20px;
}
.modal-inner-content p {
  line-height: normal;
  font-size: 16px;
  margin-bottom: 20px;
  letter-spacing: 0;
}
h6.inner-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 10px;
}

.modal-inner-content{
    overflow: auto;
    padding: 30px 20px 0px 0px;
}
.modal-inner-content::-webkit-scrollbar-thumb {
    background-color: #763F98; /* Changed to theme color */
}


/* downloads */
/* Downloads Section Styles */
/* #downloads{
    background-image: url('../images/integrated-report-2024/download-pir24-bg.svg');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: bottom;
    font-family: "Museo Sans", sans-serif;
} */
#downloads .download-button-container{
    background-image: url('../images/integrated-report-2024/download-pir24-bg-2.png');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: bottom;
    font-family: "Museo Sans", sans-serif;
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}
.carousel-container {
    position: relative;
    width: 100%;
    height: auto;
    display: flex;
    padding: 50px;
    /* background: #fff; */
    overflow-x: hidden;
    margin-left:8.33333333%;
}

.carousel-wrapper {
    position: absolute;
    width: 69%;
    height: 500px;
    overflow: hidden;
    display: flex;
    align-items: flex-end;
    transform: translate(-50%, -50%);
    top: 42%;
    left: 69%;
    border-bottom-left-radius: 15px;
}

.download-carousel{
    display: flex;
    transition: transform 0.5s ease-in-out;
    align-items: flex-end;
    margin-left: 115px;
}



.left-title-container {
    position: relative;
    left: 0;
    top: 20%;
    color: #00a19c;
    font-size: 50px;
    font-weight: bold;
    padding-left: 20px;
    line-height: 55px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 300px;
    /* height: 500px; */

    /* opacity: 0; */
    /* transform: translateX(-100%); */
}

.content-wrapper {
    /* position: absolute; */
    color: #00a19c;
    font-size: 40px;
    font-weight: bold;
    line-height: 45px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 300px;

    /* opacity: 0; */
    /* transform: translateX(100%); */

}

.subtitle {
    font-size: 16px;
    color: #000000;
    margin-bottom: 20px;
    line-height: normal;
}

.fixed-active-card {
    position: absolute;
    /* top: 50%;
    left: 40.5%; */
    /* transform: translate(-50%, -50%); */
    z-index: 3;
    opacity: 1;
    text-align: center;
    flex: 0 0 300px;
    cursor: pointer;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background: linear-gradient(to top, #763F98, #00A99D);
    padding: 20px;
    overflow: hidden;
    width: 340px;
    height: 400px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    
}

.fixed-active-card img {
    width: auto;
    height: 80%;
    border-radius: 10px;
    /* margin-bottom: 20px; */
    transition: transform 0.3s ease;
    object-fit: cover;
    position: relative;
        max-height: 300px !important;
    object-fit: contain;
}

.fixed-active-card .text {
    color: #fff;
    font-size: 20px;
    line-height: normal;
    font-weight: 600;
    text-align: left;
    position: absolute;
    bottom: 30px;
    
}

/* Add these styles for fade transitions */
.title-text, .fixed-active-card img, .fixed-active-card .text {
    transition: opacity 0.3s ease-out;
}

.title-text.fade-out, .fixed-active-card img.fade-out, .fixed-active-card .text.fade-out {
    opacity: 0;
}

.title-text.fade-in, .fixed-active-card img.fade-in, .fixed-active-card .text.fade-in {
    opacity: 1;
}

.download-button-container{
  /* position: absolute;
  top:550px; */
  margin-top: 30px;
}

.download-button .cta-text{
  line-height: normal;
}

.download-button.read-more-cta{
  color: #000;
}

#downloads {
  /* display: flex; */
  align-items: center;
  width: 100%;
}

.title-text{
     font-size: 50px;
    line-height: normal;
    font-weight: 900;
    min-height: 360px;
}
#downloads .read-more-cta span{
  color: #000;
}

@media screen and (max-width: 1500px) {
  .title-text{
     font-size: 50px;
  }
  .carousel-container{
        margin-left: 4%;
  }
  .download-item .text {
    font-size: 16px;
     /* padding: 10px; */
  }
}

@media screen and (max-width: 1366px) {
  .title-text{
     font-size: 40px;
  }
  .carousel-wrapper {
    width: 55%;
  }
}

@media (max-width:980px) {
  .title-text{
    font-size: 35px;
  }
  .content-wrapper{
    width: 30%;
  }
  .carousel-wrapper {
    width: 52%;
  }
}

@media (max-width: 768px) {
    .fixed-active-card {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        transform: none;
        z-index: 1;
        /* width: 100%;
        height: auto; */
        padding: 20px;
        margin-bottom: 20px;
        
        /* display: none; */
    }

    .content-wrapper {
        position: relative;
        color: #00a19c;
        font-size: 40px;
        font-weight: 700;
        line-height: 45px;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
    }

    .carousel-container {
        flex-direction: column;
        align-items: center;
        padding: 30px;
        /* height: auto; */
        height: 800px;
        padding-bottom: 50px;
        background-size: cover;
        background-position: bottom;
        padding-right: 0px;
        margin: 0;
    }

    .left-title-container {
        position: relative;
        left: 0;
        top: 0;
        color: #00a19c;
        font-size: 30px;
        font-weight: bold;
        padding-left: 0;
        line-height: 35px;
        z-index: 2;
        display: flex;
        flex-direction: column;
        /* align-items: center; */
        width: 100%;
        /* height: auto; */
        padding-bottom: 50px;
        height: 350px;
    }/* Original size for inactive items */
    
    
    .download-item {
        flex: 0 0 210px;
        height: 260px;
        margin: 10px 0;
        margin-right: 10px;
    }

    /* Larger size for active item */
    .download-item.active {
        opacity: 1;
        transition: all 0.5s ease, transform 0.3s ease;
        background: linear-gradient(to top, #763F98, #00A99D);
        transition-delay: transform 1s;
        flex: 0 0 250px; /* 40px wider than inactive */
        height: 300px; /* 40px taller than inactive */
        /* margin: 10px 0; */
        margin-right: 10px;
    }

    /* Image sizes */
    .download-item img {
        height: 160px; /* Default size for inactive items */
    }
    
    .download-item.active img {
        height: 200px; /* Larger for active item */
    }

    /* Text sizes */
    .download-item .text {
        /* font-size: 16px;  */
        font-size: 14px; 
        line-height: 18px;

    }

    .download-item .text {
      padding-right: 10px;
    }

    .download-item .text i:before {
      content: none;
    }
    
    .download-item.active .text {
        font-size: 20px; /* Slightly larger for active item */
    }

    /* .download-item.active img{
        width: 100%;
        height: 200px;
        object-fit: contain;
    } */

    .carousel-wrapper {
        position: relative;
        width: 100%;
        height: auto;
        overflow: hidden;
        display: flex;
        transform: none;
        top: 0;
        left: 0;
        align-items: center;
    }

    .download-item {
        /* flex: 0 0 100%; */
        margin: 10px 0;
        margin-right: 10px;
    }

    .content-wrapper .title-text {
        padding-bottom: 20px;
    }

    .download-item:hover {
        transform: none;
    }

    .carousel-wrapper {
        position: relative;
        width: 100%;
        height: 350px; /* Fixed height for carousel */
        overflow-x: auto; /* Change to auto for horizontal scrolling */
        display: flex;
        transform: none;
        top: 0;
        left: 0;
        align-items: center;
        scroll-snap-type: x mandatory; /* Enable snap scrolling */
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }

    .download-carousel{
        display: flex;
        height: 100%;
        align-items: flex-end;
        gap: 15px; /* Space between items */
        margin-left: 0;
    }

    .download-item {
        flex: 0 0 210px;
        /* height: 260px; */
        height: 100%;
        /* scroll-snap-align: center;  */
        margin: 0; /* Remove margins since we're using gap */
        transition: all 0.3s ease;
        width: 170px;
        padding: 10px;
    }

    .download-item.active {
        /* flex: 0 0 250px; */
        height: 300px;
    }

    .download-item .text {
      padding-bottom: 0px;
    }

    /* Hide scrollbar but keep functionality */
    .carousel-wrapper::-webkit-scrollbar {
        display: none;
    }
    .download-button-container {
        margin-top: 20px;
    }
    .title-text {
        font-size: 35px;
    }
    .title-text{
        min-height: auto;
    }
}

.JgeneralFooterContainer .JgeneralFooter2 {
    padding-top: 0px !important;
}



#key-messages, #financial{
  overflow: hidden !important;
}

@media screen and (max-width: 1024px) {
    /* cards */
  .card-stack{
    display: flex;
    flex-direction: column;
    align-items: center;
    gap:30px;
  }
  #energy-strategy-cards .card {
    position: relative;
    top: 0;
    left: 0;
    transform: translate(0, 50px);
    opacity: 0;
    transition: opacity 1s ease-in-out;
  }
  #energy-strategy-cards .card-stack, #energy-strategy-cards .card-wrapper{
    height: auto;
  }
  #energy-strategy-cards .card-wrapper{
    padding: 80px 0;
  }

  #energy-strategy-cards .card.in-view {
    opacity: 1;
    transform: translate(0, 0);
  }

  /* Creating Sustainable Value */
  .center-circle,
  .left-circle,
  .right-circle {
    position: absolute;
    opacity: 0;
    transition: all 1s ease-in-out;
  }

  /* Initial positions */
  .center-circle {
    top: 16%;
    left: 50%;
    transform: translate(-50%, 0%) scale(1.35);
    transform-origin: center bottom;
  }

  .left-circle {
    top: 5%;
    left: 50%;
    transform: translate(-50%, 0%);
    transform-origin: left bottom;
  }

  .right-circle {
    top: 5%;
    right: 50%;
    transform: translate(50%, 0%);
    transform-origin: right bottom;
  }

  #creating-sustainable-value.in-view .center-circle {
    opacity: 1;
    top: 63%; /* matches your mobile final value */
    transform: translate(-50%, -50%) scale(1);
  }

  #creating-sustainable-value.in-view .left-circle {
    opacity: 1;
    top: 55%;
    left: 3%;
    transform: translateY(-50%);
  }

  #creating-sustainable-value.in-view .right-circle {
    opacity: 1;
    top: 55%;
    right: 3%;
    transform: translateY(-50%);
  }

  /* Financial Performance */
  .scrol-text-animation2 .section-content .text-animation-rows .line-row {
    transition: color 0.6s ease 1s;
    transition: all 0.6s ease;
    color: #bcbcbc !important;
  }

  .scrol-text-animation2 .section-content .text-animation-rows .line-row.in-view {
   color: #00A99D !important;
  }

  #energy-strategy-cards .card .card-content{
      transform: none !important;
  }

  /* Non-financial content index */
  #accordionPanelsStayOpenExample.accordion {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }

  #accordionPanelsStayOpenExample.accordion.in-view {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  /* Energy transition strategy */
  .energy-transition-strategy h2,
  .ets-card,
  .ets-card-content {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }

  .energy-transition-strategy.in-view h2 {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0s;
  }

  .energy-transition-strategy.in-view .ets-card {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.3s;
  }

  .energy-transition-strategy.in-view .ets-card-content {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.6s;
  }

  /* Key messages */
  .key-message-content .image-column,
  .key-message-content .message-column,
  .key-message-content .quote-content,
  .key-message-content .quote-message,
  .key-message-content .quote-wrapper .bgelements,
  .key-message-content .read-more-cta {
    opacity: 0;
    transition: all 0.8s ease;
  }

  /* === Left layout === */
  .key-message-content.image-left-init .image-column {
    transform: translateX(50%);
    z-index: 2;
  }
  .key-message-content.image-left-init .message-column {
    transform: translateX(-50%);
    z-index: 1;
  }

  /* === Right layout === */
  .key-message-content.image-right-init .image-column {
    transform: translateX(-50%);
    z-index: 2;
  }
  .key-message-content.image-right-init .message-column {
    transform: translateX(50%);
    z-index: 1;
  }

  /* === On scroll into view === */
  .key-message-content.in-view .image-column,
  .key-message-content.in-view .message-column {
    transform: translateX(0);
    opacity: 1;
  }

  /* === Fade-in staggered items === */
  .key-message-content.in-view .quote-content,
  .key-message-content.in-view .quote-message {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.4s;
  }

  .key-message-content.in-view .quote-wrapper .bgelements {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.6s;
  }

  .key-message-content.in-view .read-more-cta {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.8s;
  }

  /* financial */
  #financial h2,
  #financial .financial-intro,
  #financial .tab-buttons,
  #financial .financial-bars,
  #financial .right-panel p#description {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
  }

  /* tab buttons slide up instead */
  #financial .tab-buttons {
    transform: translateY(30px);
  }

  #financial.in-view h2 {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.1s;
  }

  #financial.in-view .financial-intro {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.2s;
  }

  #financial.in-view .tab-buttons {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.3s;
  }

  #financial.in-view .financial-bars {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.4s;
  }

  #financial.in-view .right-panel p#description {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.5s;
  }

  /* financial statement */
  /* === Initial states === */
  .financial-statement-background {
    opacity: 0;
    transform: scale(1);
    transition: all 1s ease-in-out;
  }

  .financial-statement-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 1s ease;
  }

  .financial-statement-section .read-more-cta {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.8s ease;
  }

  .financial-statement-section .line-row {
    opacity: 0;
    transform: translateY(20px);
    color: #00A99D;
    transition: all 0.8s ease;
  }

  /* === In-view triggers === */
  .financial-statement-section.in-view .financial-statement-background {
    opacity: 1;
    transform: scale(1.01);
  }

  .financial-statement-section .line-row.line-in-view {
    opacity: 1;
    transform: translateY(0);
    color: #00A99D;
  }

  .financial-statement-section.in-view .financial-statement-right {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 0.6s;
  }

  .financial-statement-section.in-view .read-more-cta {
    opacity: 1;
    transform: translateX(0);
    transition-delay: 1s;
  }
  #non-financial-content-index .accordion{
    transition: all 0.8s ease;
  }
  #non-financial-content-index .accordion.in-view{
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  /* overview */
  .scrol-text-animation1 .line-row {
    opacity: 1;
    transition: color 1s linear .5s;
  }

  .scrol-text-animation1 .line-row.line-in-view {
    color: #00A99D;
  }
}

html, body {
  overscroll-behavior: none;
  touch-action: manipulation;
  scroll-behavior: smooth;
}
.card {
  position: relative;
  will-change: transform;
  --after-opacity:0;
}

.card-content {
  will-change: transform;
}

.card::after {
  content: "";
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  opacity: var(--after-opacity, 0);
  transition: opacity 0.3s ease;
  pointer-events: none;
   will-change: opacity;
}
