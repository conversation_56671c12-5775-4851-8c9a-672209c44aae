<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>PETRONAS Integrated Report 2024 - Improved Layout</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <style>
        /* Original styles */
        .chairman-photo {
            border-radius: 20px;
            overflow: hidden;
        }
        .highlight-title {
            color: #00A99D;
            font-weight: bold;
            font-size: 30px !important;
            padding-bottom: 10px !important;
            padding-top: 20px !important;
        }
        .fw-bold {
            font-size: 40px;
            font-weight: 700 !important;
        }
        .fw-p {
            font-size: 16px;
            font-weight: 300;
        }
        .fw-semibold {
            font-size: 20px !important;
            font-weight: 400 !important;
            padding-top: 30px !important;
        }

        /* Solution 1: CSS Grid Layout (Recommended) */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: 2rem;
            align-items: start;
            min-height: 100vh;
        }

        .content-grid.single-column {
            grid-template-columns: 1fr;
        }

        .image-column {
            position: sticky;
            top: 2rem;
            height: fit-content;
            max-height: calc(100vh - 4rem);
            overflow: hidden;
        }

        .text-column {
            min-height: 100vh;
        }

        /* Solution 2: Flexbox Layout Alternative */
        .content-flex {
            display: flex;
            gap: 2rem;
            align-items: flex-start;
            min-height: 100vh;
        }

        .content-flex .image-column {
            flex: 0 0 40%;
            position: sticky;
            top: 2rem;
            height: fit-content;
            max-height: calc(100vh - 4rem);
            overflow: hidden;
        }

        .content-flex .text-column {
            flex: 1;
            min-height: 100vh;
        }

        /* Solution 3: CSS Columns Layout */
        .content-columns {
            column-count: 2;
            column-gap: 2rem;
            column-fill: auto;
        }

        .content-columns .image-column {
            break-inside: avoid;
            position: sticky;
            top: 2rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .content-grid,
            .content-flex {
                display: block;
            }
            
            .image-column {
                position: static !important;
                max-height: none !important;
                margin-bottom: 2rem;
            }

            .content-columns {
                column-count: 1;
            }
        }

        /* Demo styles */
        .demo-section {
            margin: 3rem 0;
            padding: 2rem;
            border: 2px solid #00A99D;
            border-radius: 10px;
        }

        .demo-title {
            color: #00A99D;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        /* Long content simulation */
        .long-content {
            min-height: 150vh;
        }

        /* Solution 4: Text Wrap Around Image */
        .content-wrap {
            position: relative;
            text-align: justify;
            line-height: 1.6;
        }

        .content-wrap .image-float {
            float: right;
            margin: 0 0 1rem 2rem;
            max-width: 45%;
            shape-outside: margin-box;
            shape-margin: 1rem;
        }

        .content-wrap .image-float img {
            width: 100%;
            height: auto;
            border-radius: 15px;
        }

        /* Alternative: Float left */
        .content-wrap .image-float-left {
            float: left;
            margin: 0 2rem 1rem 0;
            max-width: 45%;
            shape-outside: margin-box;
            shape-margin: 1rem;
        }

        /* Clear floats */
        .content-wrap::after {
            content: "";
            display: table;
            clear: both;
        }

        @media (max-width: 768px) {
            .content-wrap .image-float,
            .content-wrap .image-float-left {
                float: none;
                max-width: 100%;
                margin: 1rem 0;
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="fw-bold text-center mb-5">Layout Solutions for Content Flow</h1>
        
        <!-- Solution 1: CSS Grid Layout -->
        <div class="demo-section">
            <h2 class="demo-title">Solution 1: CSS Grid Layout (Recommended)</h2>
            <div class="content-grid">
                <div class="image-column">
                    <div class="chairman-photo mb-3">
                        <img src="../assets/images/integrated-report-2024/chairman-image01.png" 
                             class="img-fluid" 
                             alt="Chairman Image"
                             style="background: #f0f0f0; min-height: 400px; width: 100%; object-fit: cover;">
                    </div>
                </div>
                <div class="text-column long-content">
                    <h4 class="highlight-title">Grid Layout Benefits</h4>
                    <p class="fw-p">
                        This CSS Grid solution provides the most flexible and modern approach. The image stays sticky in the left column while content flows naturally in the right column. When content is longer than the viewport height, it continues below the image area.
                    </p>
                    <p class="fw-p">
                        Key advantages:
                        • Modern CSS Grid technology
                        • Responsive design
                        • Sticky positioning for images
                        • Natural content flow
                        • Easy to maintain
                    </p>
                    <p class="fw-p">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                    </p>
                    <p class="fw-p">
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                    </p>
                    <p class="fw-p">
                        Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
                    </p>
                    <p class="fw-p">
                        Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.
                    </p>
                    <p class="fw-p">
                        Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.
                    </p>
                </div>
            </div>
        </div>

        <!-- Solution 2: Flexbox Layout -->
        <div class="demo-section">
            <h2 class="demo-title">Solution 2: Flexbox Layout</h2>
            <div class="content-flex">
                <div class="image-column">
                    <div class="chairman-photo mb-3">
                        <img src="../assets/images/integrated-report-2024/chairman-image02.jpg" 
                             class="img-fluid" 
                             alt="Chairman Image"
                             style="background: #f0f0f0; min-height: 400px; width: 100%; object-fit: cover;">
                    </div>
                </div>
                <div class="text-column long-content">
                    <h4 class="highlight-title">Flexbox Layout Benefits</h4>
                    <p class="fw-p">
                        This Flexbox solution offers excellent browser support and is easier to understand for developers familiar with flexbox. The image column has a fixed width while the text column takes the remaining space.
                    </p><br />
                    <p class="fw-p">
                        This Flexbox solution offers excellent browser support and is easier to understand for developers familiar with flexbox. The image column has a fixed width while the text column takes the remaining space.
                    </p><br />
                    <p class="fw-p">
                        This Flexbox solution offers excellent browser support and is easier to understand for developers familiar with flexbox. The image column has a fixed width while the text column takes the remaining space.
                    </p><br />
                    <p class="fw-p">
                        This Flexbox solution offers excellent browser support and is easier to understand for developers familiar with flexbox. The image column has a fixed width while the text column takes the remaining space.
                    </p><br />
                    <p class="fw-p">
                        Key advantages:
                        • Excellent browser support
                        • Familiar flexbox syntax
                        • Responsive design
                        • Sticky positioning for images
                        • Predictable behavior
                    </p>
                    <p class="fw-p">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
                    </p>
                    <p class="fw-p">
                        This content continues to flow naturally, and when it exceeds the viewport height, it will extend below the sticky image area, achieving exactly what you requested.
                    </p>
                </div>
            </div>
        </div>

        <!-- Solution 3: Text Wrap Around Image (Like your example) -->
        <div class="demo-section">
            <h2 class="demo-title">Solution 3: Text Wrap Around Image (Like Your Example)</h2>
            <div class="content-wrap">
                <div class="image-float">
                    <img src="../assets/images/integrated-report-2024/chairman-image01.png"
                         class="img-fluid"
                         alt="Award Image"
                         style="background: linear-gradient(45deg, #00A99D, #00C4B8); min-height: 300px; width: 100%; object-fit: cover;">
                </div>
                <p class="fw-p">
                    PETRONAS Leadership Centre (PLC) has been honored with the prestigious Gold Class II Award at the 42nd Malaysian Society for Occupational Safety & Health (MSOSH) Awards, held at Sunway Resort Hotel, Petaling Jaya on 11 November 2024. This significant milestone, marking our first-ever OSH recognition, is a testament to PLC's steadfast commitment to nurturing a culture where Health, Safety, and Environmental (HSE) excellence is more than a goal—it's our way of life.
                </p>
                <p class="fw-p">
                    The award was received by our esteemed Acting Chief Executive Officer (CEO) of PLC, Shazlina Shahriman, the HSE Division and the Auditee team, embodying the dedication that pulses through every member of our team. This achievement was built on the back of rigorous site verification and audit on 9 July 2024, conducted by a team of expert MSOSH auditors. It further validates PLC's proactive approach to safety and steadfast adherence to the highest standards of compliance.
                </p>
                <p class="fw-p">
                    This recognition reflects our unwavering commitment to creating a safe and healthy environment for all our stakeholders. At PLC, we believe that safety is not just a priority—it's a fundamental value that guides every decision we make and every action we take.
                </p>
                <p class="fw-p">
                    The MSOSH Gold Class II Award serves as a powerful reminder of what we can achieve when we work together towards a common goal. It reinforces our dedication to continuous improvement and our responsibility to set the standard for excellence in occupational safety and health within the industry.
                </p>
                <p class="fw-p">
                    As we celebrate this achievement, we remain committed to building upon this foundation, ensuring that our culture of safety continues to evolve and strengthen. This award is not just a recognition of our past efforts, but a catalyst for our future endeavors in creating an even safer and more sustainable workplace for everyone.
                </p>
                <p class="fw-p">
                    Moving forward, PLC will continue to champion safety excellence, leveraging this recognition to inspire further innovations in our safety practices and to maintain our position as a leader in occupational health and safety standards.
                </p>
            </div>
        </div>

        <!-- Implementation Instructions -->
        <div class="demo-section">
            <h2 class="demo-title">Implementation Instructions</h2>
            <div class="row">
                <div class="col-md-12">
                    <h5>For Text Wrap Layout (Like Your Example):</h5>
                    <p class="fw-p">Use this structure for content that should wrap around images:</p>
                    <pre style="background: #f8f9fa; padding: 1rem; border-radius: 5px; font-size: 14px;">
&lt;div class="content-wrap"&gt;
    &lt;div class="image-float"&gt;
        &lt;img src="your-image.jpg" class="img-fluid" alt="Description"&gt;
    &lt;/div&gt;
    &lt;p&gt;Your text content that will wrap around the image...&lt;/p&gt;
    &lt;p&gt;More paragraphs continue here...&lt;/p&gt;
&lt;/div&gt;</pre>

                    <h5>For Sticky Column Layout:</h5>
                    <p class="fw-p">Use CSS Grid or Flexbox solutions shown above for side-by-side layouts.</p>

                    <h5>Key Differences:</h5>
                    <ul>
                        <li><strong>Text Wrap:</strong> Content flows around the image naturally</li>
                        <li><strong>Sticky Layout:</strong> Image stays in fixed column while text scrolls</li>
                        <li><strong>Responsive:</strong> Both adapt to mobile devices automatically</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
