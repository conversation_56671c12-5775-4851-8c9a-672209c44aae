<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>PETRONAS Integrated Report 2024 - Improved Layout</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <style>
        /* Original styles */
        .chairman-photo {
            border-radius: 20px;
            overflow: hidden;
        }
        .highlight-title {
            color: #00A99D;
            font-weight: bold;
            font-size: 30px !important;
            padding-bottom: 10px !important;
            padding-top: 20px !important;
        }
        .fw-bold {
            font-size: 40px;
            font-weight: 700 !important;
        }
        .fw-p {
            font-size: 16px;
            font-weight: 300;
        }
        .fw-semibold {
            font-size: 20px !important;
            font-weight: 400 !important;
            padding-top: 30px !important;
        }

        /* Solution 1: CSS Grid Layout (Recommended) */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: 2rem;
            align-items: start;
            min-height: 100vh;
        }

        .content-grid.single-column {
            grid-template-columns: 1fr;
        }

        .image-column {
            position: sticky;
            top: 2rem;
            height: fit-content;
            max-height: calc(100vh - 4rem);
            overflow: hidden;
        }

        .text-column {
            min-height: 100vh;
        }

        /* Solution 2: Flexbox Layout Alternative */
        .content-flex {
            display: flex;
            gap: 2rem;
            align-items: flex-start;
            min-height: 100vh;
        }

        .content-flex .image-column {
            flex: 0 0 40%;
            position: sticky;
            top: 2rem;
            height: fit-content;
            max-height: calc(100vh - 4rem);
            overflow: hidden;
        }

        .content-flex .text-column {
            flex: 1;
            min-height: 100vh;
        }

        /* Solution 3: CSS Columns Layout */
        .content-columns {
            column-count: 2;
            column-gap: 2rem;
            column-fill: auto;
        }

        .content-columns .image-column {
            break-inside: avoid;
            position: sticky;
            top: 2rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .content-grid,
            .content-flex {
                display: block;
            }
            
            .image-column {
                position: static !important;
                max-height: none !important;
                margin-bottom: 2rem;
            }

            .content-columns {
                column-count: 1;
            }
        }

        /* Demo styles */
        .demo-section {
            margin: 3rem 0;
            padding: 2rem;
            border: 2px solid #00A99D;
            border-radius: 10px;
        }

        .demo-title {
            color: #00A99D;
            font-weight: bold;
            margin-bottom: 1rem;
        }

        /* Long content simulation */
        .long-content {
            min-height: 150vh;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="fw-bold text-center mb-5">Layout Solutions for Content Flow</h1>
        
        <!-- Solution 1: CSS Grid Layout -->
        <div class="demo-section">
            <h2 class="demo-title">Solution 1: CSS Grid Layout (Recommended)</h2>
            <div class="content-grid">
                <div class="image-column">
                    <div class="chairman-photo mb-3">
                        <img src="../assets/images/integrated-report-2024/chairman-image01.png" 
                             class="img-fluid" 
                             alt="Chairman Image"
                             style="background: #f0f0f0; min-height: 400px; width: 100%; object-fit: cover;">
                    </div>
                </div>
                <div class="text-column long-content">
                    <h4 class="highlight-title">Grid Layout Benefits</h4>
                    <p class="fw-p">
                        This CSS Grid solution provides the most flexible and modern approach. The image stays sticky in the left column while content flows naturally in the right column. When content is longer than the viewport height, it continues below the image area.
                    </p>
                    <p class="fw-p">
                        Key advantages:
                        • Modern CSS Grid technology
                        • Responsive design
                        • Sticky positioning for images
                        • Natural content flow
                        • Easy to maintain
                    </p>
                    <p class="fw-p">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                    </p>
                    <p class="fw-p">
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                    </p>
                    <p class="fw-p">
                        Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
                    </p>
                    <p class="fw-p">
                        Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.
                    </p>
                    <p class="fw-p">
                        Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.
                    </p>
                </div>
            </div>
        </div>

        <!-- Solution 2: Flexbox Layout -->
        <div class="demo-section">
            <h2 class="demo-title">Solution 2: Flexbox Layout</h2>
            <div class="content-flex">
                <div class="image-column">
                    <div class="chairman-photo mb-3">
                        <img src="../assets/images/integrated-report-2024/chairman-image02.jpg" 
                             class="img-fluid" 
                             alt="Chairman Image"
                             style="background: #f0f0f0; min-height: 400px; width: 100%; object-fit: cover;">
                    </div>
                </div>
                <div class="text-column long-content">
                    <h4 class="highlight-title">Flexbox Layout Benefits</h4>
                    <p class="fw-p">
                        This Flexbox solution offers excellent browser support and is easier to understand for developers familiar with flexbox. The image column has a fixed width while the text column takes the remaining space.
                    </p>
                    <p class="fw-p">
                        Key advantages:
                        • Excellent browser support
                        • Familiar flexbox syntax
                        • Responsive design
                        • Sticky positioning for images
                        • Predictable behavior
                    </p>
                    <p class="fw-p">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
                    </p>
                    <p class="fw-p">
                        This content continues to flow naturally, and when it exceeds the viewport height, it will extend below the sticky image area, achieving exactly what you requested.
                    </p>
                </div>
            </div>
        </div>

        <!-- Implementation Instructions -->
        <div class="demo-section">
            <h2 class="demo-title">Implementation Instructions</h2>
            <div class="row">
                <div class="col-md-12">
                    <h5>Step 1: Add the CSS</h5>
                    <p class="fw-p">Copy the CSS grid or flexbox styles from this demo into your existing stylesheet.</p>
                    
                    <h5>Step 2: Update HTML Structure</h5>
                    <p class="fw-p">Replace Bootstrap's row/col classes with the new layout classes:</p>
                    <ul>
                        <li>Change <code>class="row"</code> to <code>class="content-grid"</code> (or <code>content-flex</code>)</li>
                        <li>Change <code>class="col-md-5"</code> to <code>class="image-column"</code></li>
                        <li>Change <code>class="col-md-7"</code> to <code>class="text-column"</code></li>
                    </ul>
                    
                    <h5>Step 3: Test Responsiveness</h5>
                    <p class="fw-p">The layout automatically adapts to mobile devices by stacking vertically.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
