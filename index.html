<html lang="en">

<head>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-564SZHN');</script>
    <!-- End Google Tag Manager -->
    <meta charset="utf-8">
    <title>PETRONAS Integrated Report and Financial Report 2024</title>
    <meta name="description"
        content="PETRONAS delivered a strong set of financial and operational results for the year 2024. Come and find out more though our Integrated Report for 2024.">
    <meta property="og:locale" content="en_MYS">
    <meta property="og:type" content="website">
    <meta property="og:title" content="PETRONAS Integrated Report and Financial Report 2024">
    <meta property="og:description"
        content="PETRONAS delivered a strong set of financial and operational results for the year 2024. Come and find out more though our Integrated Report for 2024.">
    <meta property="og:url" content="https://www.petronas.com/integrated-report/">
    <meta property="og:site_name" content="www.petronas.com/integrated-report">
    <meta property="og:image:type" content="image/png">
    <meta property="og:image" itemprop="image"
        content="https://www.petronas.com/integrated-report/images/petronas-ir20-og.png">
    <meta property="og:image:secure_url" itemprop="image"
        content="https://www.petronas.com/integrated-report/images/petronas-ir20-og.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="660">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1,user-scalable=0" />
    <link rel="icon" href="../assets/images/favicon/favicon.png" sizes="32x32">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" as="style" />
    <link rel="preload" href="../assets/css/app.css" as="style" />
    <link rel="preload" href="../assets/plugins/bootstrap-multiselect/css/bootstrap-multiselect.min.css" as="style" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
        integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.0/css/all.min.css"
        integrity="sha512-10/jx2EXwxxWqCLX/hHth/vu2KY3jCF70dCQB8TSgNjbCVAC/8vai53GfMDrO2Emgwccf2pJqxct9ehpzG+MTw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <link rel="stylesheet" href="../assets/plugins/bootstrap-multiselect/css/bootstrap-multiselect.min.css" />
    <link href="../assets/css/app.css" rel="stylesheet" type="text/css">
</head>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-564SZHN"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
    <nav class="navbar navbar-expand-lg fixed-top" id="navbar">
        <div class="container">
            <a class="navbar-brand" href="https://www.petronas.com/">
                <img src="../assets/images/integrated-report-2024/petronas-logo.svg" class="img-fluid js-navbar-img"
                    alt="PETRONAS - Integrated Report 2024" />
            </a>
            <button type="button" class="hamburger hamburger--spin navbar-toggler" id="navbar-toggler"
                data-bs-toggle="collapse" data-bs-target="#mainNavbar" aria-controls="mainNavbar" data-expanded="true"
                aria-label="Toggle navigation">
                <span class="hamburger-box">
                    <span class="hamburger-inner"></span>
                </span>
            </button>
            <div class="navbar-collapse collapse" id="mainNavbar">
                <ul class="navbar-nav ml-auto mr-30 nav-bar-mobile">
                    <li class="nav-item">
                        <a href="https://www.petronas.com/integrated-report-2024/#overview" class="nav-link js-header-link">Overview</a>
                    </li>
                    <li class="nav-item">
                        <a href="../integrated-report-2024/#financial-highlights" class="nav-link js-header-link">Financial Highlights</a>
                    </li>
                    <li class="nav-item">
                        <a href="../integrated-report-2024/#key-messages" class="nav-link js-header-link">Key Messages</a>
                        <!-- <a href="#key-messages" class="nav-link js-header-link">Leaders Message</a> -->
                    </li>
                    <li class="nav-item">
                        <a href="../integrated-report-2024/#strategic-review" class="nav-link js-header-link">Strategic Review</a>
                    </li>
                    <li class="nav-item">
                        <a href="../integrated-report-2024/#sustainable-review" class="nav-link js-header-link">Sustainability</a>
                    </li>
                    <li class="nav-item">
                        <a href="../integrated-report-2024/#non-financial-content-index" class="nav-link js-header-link">Non-financial Index</a>
                    </li>
                    <li class="nav-item hide-on-desktop">
                        <a href="https://www.petronas.com/integrated-report-2024/reports" class="nav-link">Report</a>
                    </li>
                </ul>
            </div>

            <div class="header-download-button">
                <a href="#" id="header-download-button" data-bs-toggle="modal" data-bs-target="#downloadModal">
                    <img class="img-fluid" src="../assets/images/global/ir-download-button-02.png"
                        alt="PETRONAS Integrated Report 2024 - Download Reports">
                </a>
            </div>
        </div>
    </nav><!-- Modal -->
    <div class="modal fade" id="downloadModal" tabindex="-1" aria-labelledby="downloadModal" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <a href="#" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></a>
                    <div class="modal-upper">
                        <h4 class="modal-title theme-green fw-900 underline">PDF Downloads</h4>
                        <div class="modal-pdf-max-height">
                            <div class="accordion" id="accordionPanelsStayOpenExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="panelsStayOpen-headingOne">
                                        <a href="../assets/pdf/by-section/PETRONAS-Integrated-Report-2024.pdf" target="_blank"
                                            class="accordion-button collapsed GAEventTrack" eventName="PDF_PIR2024_PIR2024">
                                            PETRONAS Integrated Report 2024
                                        </a>
                                    </h2>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="panelsStayOpen-headingTwo">
                                        <a class="accordion-button" href="#" data-bs-toggle="collapse"
                                            data-bs-target="#panelsStayOpen-collapseTwo" aria-expanded="false"
                                            aria-controls="panelsStayOpen-collapseTwo">
                                            By Section
                                        </a>
                                    </h2>
                                    <div id="panelsStayOpen-collapseTwo" class="accordion-collapse collapse show"
                                        aria-labelledby="panelsStayOpen-headingTwo">
                                        <div class="accordion-body">
                                            <ul>
                                                <li>
                                                    <a href="../assets/pdf/by-section/1. PIR24_Basis of This Report.pdf"
                                                        target="_blank">
                                                        Basis of This Report
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="../assets/pdf/by-section/2. PIR24_We Are Passionate About Progress.pdf"
                                                        target="_blank">
                                                        We Are Passionate About Progress
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="../assets/pdf/by-section/3. PIR24_Key Messages.pdf"
                                                        target="_blank">
                                                        Key Messages
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="../assets/pdf/by-section/4. PIR24_How We Create Value.pdf"
                                                        target="_blank">
                                                        How We Create Value
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="../assets/pdf/by-section/5. PIR24_Strategic Review.pdf"
                                                        target="_blank">
                                                        Strategic Review
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="../assets/pdf/by-section/6. PIR24_Sustainability Performance Review.pdf"
                                                        target="_blank">
                                                        Sustainability Performance Review
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="../assets/pdf/by-section/7. PIR24_Commitment to Governance.pdf"
                                                        target="_blank">
                                                        Commitment To Governance
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="../assets/pdf/by-section/8. PIR24_Financial Review and Other Information.pdf"
                                                        target="_blank">
                                                        Financial Review and Other Information
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="panelsStayOpen-headingThree">
                                        <a class="accordion-button collapsed GAEventTrack" eventName="PDF_PIR2024_AFS2024"
                                            href="../assets/pdf/PIR 2024_Audited Financial Statements.pdf" target="_blank">
                                            PETRONAS Audited Financial Statements 2024
                                        </a>
                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-bottom">
                        <div class="cta cta-black">
                            <a href="#" data-download-file="assets/pdf/by-section/PETRONAS-Integrated-Report-2024.pdf"
                                data-filename="PETRONAS-Integrated-Report-2024.pdf" target="_blank" id="download-all"
                                class="flex-end-md GAEventTrack" eventName="PDF_PIR2024_PIR2024">
                                <img src="../assets/images/global/pet-icon-circle-download-colour.svg">
                                <p>Download All</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <section class="section-preloader">
        <div class="container">
            <div class="row justify-content-center align-items-center">
                <div class="preloader">
                    <div class="preload-img">
                        <img src="../assets/images/integrated-report-2024/petronas-new-logo-white.png" class="img-fluid"
                            alt="PETRONAS - Integrated Report 2024">
                    </div>
                    <div class="preload-title">
                        <p class="theme-white font24 fw-500">Integrated Report 2024</p>
                        <div class="preload-bar">
                            <div class="preload-inner" id="preload-inner" style="width: 0%;"></div>
                            <input type="hidden" id="progress_width" value="0">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div id="home">
        <section class="section" id="sustainable-future">
            <div class="line-animation-columns-container  main-banner" id="highlight-1">
                <div class="line-animation-section line-animation-row">
                <video class="line-animation-video" autoplay muted loop playsinline>
                    <source src="../assets/videos/PIR-banner-video.mp4" type="video/mp4" />
                </video>
                <div class="line-animation-panel-wrapper">
                    <div class="line-animation-panel1 line-animation-panel">
                        <div class="line-animation-panel-content"></div>
                    </div>
                    <div class="line-animation-panel2 line-animation-panel">
                        <div class="line-animation-panel-content"></div>
                    </div>
                    <div class="line-animation-panel3 line-animation-panel">
                        <div class="line-animation-panel-content"></div>
                    </div>
                    <div class="line-animation-panel4 line-animation-panel">
                        <div class="line-animation-panel-content"></div>
                    </div>
                </div>
                <div class="line-animation-content">
                    <div class="line-animation-title text-animation-auto split-chars">
                        <span class="highlighted green"><span>Chairman's</span> <span>Letter<span></span>
                    </div>
                </div>
                </div>
            </div>
        </section>
    </div>

    <style>
    .chairman-photo {
      border-radius: 20px;
      overflow: hidden;
    }
    .gradient-bg {
      background: linear-gradient(to top, #00A19B, transparent);
      padding: 1rem;
      color: white;
    }
    .highlight-title {
      color: #00A99D;
      font-weight: bold;
      font-size: 30px !important;
      padding-bottom: 10px !important;
      padding-top: 20px !important;
    }
    .fw-bold{
        font-size: 40px;
        font-weight: 700 !important;
    }
    .fw-p{
        font-size: 16px;
        font-weight: 300;
    }
    .fw-semibold{
        font-size: 20px !important;
        font-weight: 400 !important;
        padding-top: 30px !important;
    }
    </style>

    <div class="container py-5">
        <a href="https://www.petronas.com/integrated-report-2024/" class="d-inline-flex align-items-center text-decoration-none" style="color: #000;">
            <img src="../assets/images/integrated-report-2024/back-button01.png"> Back to Home
        </a>

        <br /><h2 class="fw-bold">Tan Sri Dato' Seri Mohd Bakke Salleh</h2>

        <p class="fw-semibold">Dear Stakeholders,</p><br />
        <p class="fw-p">
        Against a backdrop of persistent volatility that defined 2024, the PETRONAS Group remained steadfast in delivering our purpose. 
        Across the world, intensifying geopolitical risks and ongoing conflicts further amplified the urgent need to reinforce energy systems and ensure security of supply. 
        For PETRONAS, our Energy Transition Strategy remains a clear guide for the Group as we work to fulfil expectations of our stakeholders.
        </p>

        <div class="row my-4">
            <div class="col-md-5">
                <div class="chairman-photo mb-3">
                <img src="../assets/images/integrated-report-2024/chairman-image01.png" class="img-fluid" alt="Chairman Tan Sri Dato' Seri Mohd Bakke Salleh">
                <!-- Replace with actual image path or URL -->
                </div>
                <!-- <p class="text-center fw-semibold">Chairman<br>Tan Sri Dato' Seri Mohd Bakke Salleh</p> -->
            </div>

            <div class="col-md-7">
                <p class="fw-p">
                On behalf of the Board, I am proud to recognise the achievements of our people, whose invaluable dedication and commitment 
                have delivered a strong performance for the year in review, strengthening our foundations to create long-term sustainable value for our stakeholders.
                </p><br />

                <h4 class="highlight-title">50 Years of Nation-Building</h4>
                <p class="fw-p">
                PETRONAS commemorated its 50th anniversary in 2024. The milestone coincided with a period of uncertainty in the global operating environment. 
                Despite the challenges, the PETRONAS Board and Executive Leadership Team continued to steer the organisation towards its purpose 
                to be a progressive energy and solutions partner. In doing so, our efforts were channelled towards ensuring security of supply 
                and generating profitable long-term growth.
                </p><br />

                <p class="fw-p">
                For the year in review, PETRONAS made the scheduled dividend payment of RM32 billion to the Government of Malaysia. 
                As the sole contributor to the National Trust Fund, PETRONAS also continued to make its contribution of RM500 million in the same period, 
                resulting in a cumulative RM13.5 billion contribution to the Fund as of 2024.
                </p><br />

                <p class="fw-p">
                Since its establishment in 1974, PETRONAS has contributed RM1.5 trillion to the nation's economy, in the form of dividends, taxes, cash payments and others.
                </p><br />

                <p class="fw-p">
                Even as we contend with the intensifying pressures in the industry, I am also pleased to note that PETRONAS, as a key driver 
                of Malaysia’s National Energy Transition Roadmap, has stood resolute in delivering the targets under the Net Zero Carbon Emissions by 2050 Pathway, 
                having successfully achieved our short-term emissions reduction target in 2024.
                </p><br />

                <p class="fw-p">
                In that same vein, the Group also continued to uphold its commitment to pursue strengthened governance and compliance 
                to meet the expectations of global stakeholders.
                </p><br />
            </div>
        </div>

        <h4 class="highlight-title">Making Impactful Progress</h4><br />

        <div class="row my-4">
            <div class="col-md-6">
                <p class="fw-p">
                PETRONAS believes that true progress is achieved when organisations and communities grow together. Since inception, PETRONAS has collaborated with stakeholders and communities wherever we operate to deliver meaningful impact where it is most needed. For 50 years, the Group has remained closely attuned to societal development, tailoring our signature programmes to the demands of the era. In 2024, PETRONAS contributed RM700 million to more than 400 social impact programmes.
                </p><br />

                <p class="fw-p">
                In the nascent years of Malaysia’s oil and gas industry, we focused on nurturing talent through education by introducing the PETRONAS Education Sponsorship Programme (PESP), which has benefitted 39,000 students since 1975. We also established the Institut Teknologi PETRONAS (INSTEP) and Universiti Teknologi PETRONAS (UTP), which has been recognised as the Number One university in Malaysia by the Times Higher Education World University Rankings.
                </p><br />

                <p class="fw-p">
                Later, PETRONAS expanded its focus to uplifting societies through our Memampankan Ekonomi Asas Rakyat programme, providing access to necessities like water and electricity through the ‘Water for Life’ initiative and the installation of solar panels in rural areas. As our operations grew across borders, these initiatives were replicated and further customised to the needs of global communities.
                </p><br />

                <p class="fw-p">
                In recent years, PETRONAS has introduced several environmental and social programmes, such as Rigs-to-Reef, mangrove conservation, the Imbak Canyon Conservation project, COVID-19 aid and flood relief. These efforts have had a lasting positive impact on Malaysians and local communities where PETRONAS operates
                </p><br />
            </div>
        
            <div class="col-md-6">
                <div class="chairman-photo mb-3">
                    <img src="../assets/images/integrated-report-2024/chairman-image02.jpg" class="img-fluid" alt="Chairman Tan Sri Dato' Seri Mohd Bakke Salleh">
                    <!-- Replace with actual image path or URL -->
                </div>
                <!-- <p class="text-center fw-semibold">Chairman<br>Tan Sri Dato' Seri Mohd Bakke Salleh</p> -->
            </div>
        </div>

        <div class="row my-4">
            <div class="col-md-12">
                <div class="chairman-photo mb-3">
                    <img src="../assets/images/integrated-report-2024/chairman-image03.jpg" class="img-fluid" alt="Chairman Tan Sri Dato' Seri Mohd Bakke Salleh">
                </div>
            </div>


            <div class="col-md-12">
            <h4 class="highlight-title">Strengthening Foundations for Future Growth</h4><br />
                    <p class="fw-p">
                    PETRONAS has played a crucial role in shaping Malaysia’s oil and gas landscape. As a regulator, we nurtured a competitive ecosystem by introducing balanced fiscal terms in Production Sharing Contracts to grow the value pie. We also dutifully managed Petroleum Arrangement Contractors, and Oil and Gas Services and Equipment (OGSE) players to ensure compliance with high standards and governance. 
                    </p><br />
                    <p class="fw-p">
                    Through the Vendor Development Programme (VDP) and VDPx, PETRONAS has supported 171 small and medium enterprise (SME) vendors since 1993, where 20 have successfully expanded their businesses into international markets. On the vendor financing front, 357 vendors have benefitted from RM2.2 billion in funding to expand their operations since the Vendor Finance Programme (VFP) was introduced in 2018.
                    </p><br />
                    <p class="fw-p">
                    Meanwhile, the PETRONAS Supplier Support Programme (PSSP) which was launched in 2024, aims to equip SMEs with sustainability capabilities and access to transition financing necessary to remain competitive in a low-carbon future. In the year under review, 417 vendors have enrolled in the programme. 
                    </p><br />
                    <p class="fw-p">
                    At PETRONAS, we believe that sustainable growth is only possible when all industry players grow in tandem. With increased complexity in the macro environment putting downward pressure on margins, innovation and efficiency are foreseen to be key factors in project feasibility and profitability. 
                    </p><br />
                    <p class="fw-p">
                    Moving forward, it will be more pivotal than ever that Petroleum Arrangement Contractors, OGSE players and technology providers strike mutually beneficial partnerships to jointly contribute to a progressive sector in Malaysia.
                    </p><br />
            </div>
        </div>
        
        <div class="row my-4">
            <div class="col-md-12">
                <h4 class="highlight-title">The Year Ahead</h4><br />
                    <p class="fw-p">
                    As we look ahead to 2025, we anticipate unrelenting volatility to persist with geopolitical shifts continuing to redefine the trade landscape and realign priorities in the energy transition. The industry is already responding to these shifts with sharpened focus on portfolio resilience, prudent financial management and strategic industry partnerships. In this regard, PETRONAS has put in place a series of transformative measures that will best position us to continue executing our Energy Transition Strategy, deliver long-term value to our stakeholders, and fulfil our purpose as a progressive energy and solutions partner. 
                    </p><br />
                    <p class="fw-p">
                    With Malaysia assuming the chair of ASEAN in 2025, PETRONAS also looks forward to playing a part in strengthening regional collaboration to shape the future of energy for this region. 
                    </p><br />
                    <p class="fw-p">
                    In closing, I would like to take this opportunity to thank Datuk Johan Mahmood Merican, Dato Haji Ibrahim Haji Baki and Datuk KY Mustafa for their exemplary leadership and contributions as members of the PETRONAS Board of Directors. I wish them every success in their future endeavours.
                    </p><br />
                    <p class="fw-p">
                    I would also like to extend a warm welcome to our new Board members namely, Datuk Dr Shahrazat Haji Ahmad and Datuk Seri Abdul Rasheed Ghaffour. Their vast experience and diverse perspectives will be invaluable in steering PETRONAS forward. 
                    </p><br />
                    <p class="fw-p">
                    On behalf of the Board, I would like to record my heartfelt appreciation to Tan Sri Tengku Muhammad Taufik, President and Group CEO of PETRONAS, and his Executive Leadership Team for steering the Group towards delivering business strategies with agility and integrity. My deep appreciation also goes out to all employees for their sheer dedication and upholding of our Shared Values in discharging their duties. 
                    </p><br />
                    <p class="fw-p">
                    Finally, I am grateful to our valued stakeholders, including the Federal and State Governments of Malaysia, our host countries, partners and customers for their continued trust and support. As we set forth in this next chapter, PETRONAS looks forward to building on these enduring partnerships to continue enriching lives in the next 50 years and beyond.
                </p><br />
            </div>
            
            <div class="col-md-12">
                <div class="chairman-photo mb-3">
                    <img src="../assets/images/integrated-report-2024/chairman-image04.jpg" class="img-fluid" alt="Chairman Tan Sri Dato' Seri Mohd Bakke Salleh">
                </div>
            </div>


        </div>
    </div>

    <div class="JgeneralFooterContainer">
        <div class="JgeneralFooter JgeneralFooter2">
            <div class="JgeneralFooterPattern">
                <img src="../assets/images/footer/footerLeftPattern.png">
                <img src="../assets/images/footer/footerRightPattern.png">
            </div>
            <div class="JgeneralFooterInner">
                <div class="JgeneralFooterTop">
                    <div class="JgeneralFooterTopNavi">
                        <div class="JgeneralFooterTopNaviLeft">
                            <ul>
                                <li class="nav-item">
                                    <a href="https://www.petronas.com/terms-of-use" class="nav-link">Terms of Use</a>
                                </li>
                                <li class="nav-item">
                                    <a href="https://www.petronas.com/privacy-statement" class="nav-link">Privacy
                                        Statement</a>
                                </li>
                                <li class="nav-item">
                                    <span
                                        class="ot-sdk-show-settings onetrust-footer-link nav-link ot-sdk-show-settings onetrust-footer-link nav-link-">Cookies
                                        Settings</span>
                                </li>
                            </ul>
                        </div>
                        <div class="JgeneralFooterTopNaviRight">

                            <ul class="clearfix nav">
                                <li class="nav-item">
                                    <a href="https://www.petronas.com/whistleblowing" target="_blank"
                                        class="nav-link">Whistleblowing</a>
                                </li>
                                <li class="nav-item">
                                    <a href="https://www.petronas.com/scam-notice" target="_blank" class="nav-link">Scam
                                        Notice</a>
                                </li>
                                <li class="nav-item">
                                    <a href="https://www.petronas.com/connect-with-us" target="_blank"
                                        class="nav-link">Connect With Us</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="JgeneralFooterBtm">
                    <div class="JgeneralFooterBtmLeft">
                        <div id="block-copyright"
                            class="block block-block-content block-block-contenta37f5ae4-c50f-4f11-92c6-b9fc1d4acf48">
                            <div class="content">
                                <div class="clearfix text-formatted field--name-body">
                                    <p>Copyright © 2025 Petroliam Nasional Berhad (PETRONAS)
                                        <sub><sup>(20076-K)</sup></sub>.<br>
                                        All rights reserved.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="JgeneralFooterBtmRight">
                        <ul class="clearfix nav">
                            <li class="nav-item">
                                <a href="https://www.facebook.com/petronas/" target="_blank"
                                    class="JindexFooterFB JsocialFB JindexNaviRightBtmSocialFB nav-link JindexFooterFBJsocialFBJindexNaviRightBtmSocialFB">Facebook</a>
                            </li>
                            <li class="nav-item">
                                <a href="https://www.instagram.com/petronas/" target="_blank"
                                    class="JindexFooterIN JsocialIN JindexNaviRightBtmSocialIN nav-link JindexFooterINJsocialINJindexNaviRightBtmSocialIN">Instagram</a>
                            </li>
                            <li class="nav-item">
                                <a href="https://www.tiktok.com/@petronas_official" target="_blank"
                                    class="JsocialTok JindexNaviRightBtmSocialTok JindexFooterTok nav-link JsocialTokJindexNaviRightBtmSocialTokJindexFooterTok">TikTok</a>
                            </li>
                            <li class="nav-item">
                                <a href="https://twitter.com/Petronas" target="_blank"
                                    class="JindexFooterTT JsocialTT JindexNaviRightBtmSocialTT nav-link JindexFooterTTJsocialTTJindexNaviRightBtmSocialTT">Twitter</a>
                            </li>
                            <li class="nav-item">
                                <a href="https://www.youtube.com/user/PETRONASOfficial" target="_blank"
                                    class="JindexFooterYT JsocialYT JindexNaviRightBtmSocialYT nav-link JindexFooterYTJsocialYTJindexNaviRightBtmSocialYT">YouTube</a>
                            </li>
                            <li class="nav-item">
                                <a href="https://www.linkedin.com/company/petronas" target="_blank"
                                    class="JindexFooterLI JsocialLI JindexNaviRightBtmSocialLI nav-link JindexFooterLIJsocialLIJindexNaviRightBtmSocialLI">LinkedIn</a>
                            </li>

                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="sticky-nav-bar" id="sticky-nav-bar" style="position: fixed;">
        <ul class="nav sticky-sidebar">
            <li class="nav-item padding-sticky-nav">
                <a href="reports" class="GAEventTrack"><img class="sticky-nav-icon"
                        src="../assets/images/sticky-navbar/statistic-icon.svg"></a>
            </li>
            <li class="nav-item padding-sticky-nav">
                <a href="https://www.petronas.com/integrated-report-2023/" class="GAEventTrack" target="_blank"><img
                        class="sticky-nav-icon" src="../assets/images/sticky-navbar/report-icon.svg"></a>
            </li>
            <li class="nav-item padding-sticky-nav">
                <a href="#" class="GAEventTrack" id="header-download-button" data-bs-toggle="modal"
                    data-bs-target="#downloadModal"><img class="sticky-nav-icon"
                        src="../assets/images/sticky-navbar/download-icon.svg"></a>
            </li>
        </ul>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p"
        crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js"
        integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script>
    document.addEventListener("DOMContentLoaded", () => {
        const section = document.querySelector("#downloads");

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
            if (entry.isIntersecting) {
                section.classList.add("active");
                observer.unobserve(section); // Run only once
            }
            });
        }, {
            root: null,
            threshold: 0.5 // trigger when 50% of section is visible
        });

        observer.observe(section);
    });
    </script>

    <script type="text/javascript">
        $(".hamburger").on("click", function () {
            $(this).toggleClass("is-active");
            var getSibling = $(this).siblings(".navbar-collapse");
            $("body").toggleClass("nav-is-open");

            setTimeout(function () {
                if (getSibling.hasClass("show")) {
                    getSibling.find(".navbar-nav").delay(500).addClass("nav-open");
                } else {
                    getSibling.find(".navbar-nav").removeClass("nav-open");
                }
            }, 500);
        });

        $("[data-download-file]").on("click", function (event) {
            event.preventDefault();
            var url = $(this).data("download-file");
            var filename = $(this).data("filename");
            saveAs(url, filename);
        });

        $(function () {
            setTimeout(() => {
                preloader();
            }, 2000);

            function preloader() {
                $(".section-preloader")
                    .css("visibility", "visible")
                    .fadeIn(500, function () {
                        $(".preload-inner")
                            .delay(1000)
                            .queue(function () {
                                $(".section-preloader").addClass("animate_top"); // hide the preloader once the animation is done
                                $(document).trigger('start:animation');
                                setTimeout(() => {
                                    $(this).dequeue();
                                }, 1000);
                            });
                    });
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"
        integrity="sha512-7eHRwcbYkK4d9g/6tD/mhkf++eoTHwpNM9woBxtPUBWm67zeAfFC+HrdoE2GanKeocly/VxeLvIqwvCdk7qScg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"
        integrity="sha512-onMTRKJBKz8M1TnqqDuGBlowlH0ohFzMXYRNebz+yOcc5TQr/zAKsthzhuv0hiyUKEiQEQXEynnXCvNTOk50dg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://www.youtube.com/iframe_api"></script>
    <script src="https://unpkg.com/split-type"></script>
    <script src="https://npmcdn.com/chart.js@latest/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2"></script>
   
    <!-- Scroll Trigger and other settings -->
    <script type="text/javascript">
        $(document).ready(function () {
            var hash = window.location.hash;
            if (hash) {
                $('html, body').animate({
                    scrollTop: $(hash).offset().top
                }, 500);
            }

            $('a[href*="#"]').on('click', function (event) {
                var target = $(this).attr('href');

                // Prevent default behavior for all hash links
                event.preventDefault();

                // Ignore if href is just "#"
                if (target === "#") return;

                // Proceed if the element with the target ID exists
                var $target = $(target);
                if ($target.length) {
                    $('html, body').animate({
                        scrollTop: $target.offset().top
                    }, 500, function () {
                        window.location.hash = target;
                    });
                }
            });

            if (window.matchMedia('(min-width: 640px)').matches) {
                $('.js-tl-item').hover(function () {
                    $(this).siblings().find('h4').css('display', 'none');
                    $(this).find('h4').css('display', 'block');
                }, function () {
                    $(this).siblings().find('h4').css('display', 'block');
                });
            }
        });

        $(".js-header-link").on("click", function (e) {
            if (window.matchMedia("(min-width: 992px)").matches) {
                // do nothing
            } else {
                event.preventDefault(); // Prevent default link behavior
                $(".hamburger").click();
                // Scroll to the section after a short delay to allow sidebar to close
                setTimeout(() => {
                    const headerHeight = $('#navbar').outerHeight();
                    const targetId = $(this).attr('href').substring(1); // Get target section ID
                    const targetElement = $('#' + targetId);
                    if (targetElement.length) {
                        $('html, body').animate({
                            scrollTop: targetElement.offset().top - (headerHeight / 2)
                        }, 500); // Adjust scroll animation speed as needed
                    }
                }, 300); // Adjust delay time as needed
            }
        })

        gsap.registerPlugin(ScrollTrigger);
        ScrollTrigger.config({
            ignoreMobileResize: true, // Prevents issues with mobile browser address bar showing/hiding
        });
        window.addEventListener('touchstart', () => ScrollTrigger.refresh(), { passive: true });
        window.addEventListener('touchmove', () => ScrollTrigger.refresh(), { passive: true });
        

        $(document).on("start:animation", function () {
            $(".sticky-nav-bar").addClass("complete");

           
            // pir 2024
            const isTablet = window.innerWidth <= 1024;
            const isMobile = window.innerWidth <= 768;
            $(document).on("click", ".read-more-cta[href*='.pdf']", function(e) {
                // Stop propagation to prevent other handlers from catching this
                e.stopPropagation();           
                // Get the href
                const pdfUrl = $(this).attr('href');
                // Open the PDF directly
                window.location.href = pdfUrl;
                return true;
            });

            // navbar
            let lastScrollTop = 0;
            $(window).on("scroll", function () {
            let currentScroll = $(this).scrollTop();

            if (currentScroll <= 0) {
                // At top of page
                $("#navbar").removeClass("hide show");
            } else if (currentScroll > lastScrollTop) {
                // Scrolling down
                $("#navbar").removeClass("show").addClass("hide");
            } else {
                // Scrolling up
                $("#navbar").removeClass("hide").addClass("show");
            }
            lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
            });

            //text split
            let lineSplit, wordSplit, charSplit;

            // === SPLIT SETUP ===
            function runSplitTypes() {
            if (lineSplit) lineSplit.revert();
            if (wordSplit) wordSplit.revert();
            if (charSplit) charSplit.revert();

            // Split by lines
            lineSplit = new SplitType(".split-lines", { types: "lines" });
            $(".split-lines .line").addClass("line-row");

            // Split by words
            wordSplit = new SplitType(".split-words", { types: "words" });

            // Split by chars (also gets lines & words)
            charSplit = new SplitType(".split-chars", { types: "chars" });
            }
            // === ON LOAD ===
            runSplitTypes();

            // === ON RESIZE ===
            let windowWidth = window.innerWidth;
            window.addEventListener("resize", () => {
            if (window.innerWidth !== windowWidth) {
                windowWidth = window.innerWidth;
                runSplitTypes();
            }
            });

            function triggerLineAnimationsOnScroll() {
                $(".line-animation-columns-container").each(function () {
                    var $container = $(this);

                    // Skip if already animated
                    if ($container.hasClass("animated")) return;

                    // Optional readiness check
                    var isReady = $container.attr("data-trigger-ready") === "true" || !$container.is("[data-after-cards]");

                    // Viewport visibility check
                    var windowHeight = $(window).height();
                    var elementTop = $container.offset().top;
                    var elementBottom = elementTop + $container.outerHeight();
                    var scrollTop = $(window).scrollTop();
                    var scrollBottom = scrollTop + windowHeight;

                    var isVisible = elementTop < scrollBottom - windowHeight * 0.4 && elementBottom > scrollTop;

                    // Trigger animation
                    if (isVisible && isReady) {
                        setTimeout(function () {
                            $container.find(".line-animation-panel-content").addClass("slide-out");
                            $container.addClass("animated");
                            animateTextFill($container); // make sure animateTextFill is globally defined
                        }, 750);
                    }
                });
            }

            // Call once on page load
            triggerLineAnimationsOnScroll();

            // Also call on scroll
            $(window).on("scroll", function() {
                triggerLineAnimationsOnScroll();
            });


            window.addEventListener("touchmove", triggerLineAnimationsOnScroll, { passive: true });
            // Optional: trigger on resize
            $(window).on("resize", triggerLineAnimationsOnScroll);
            

            // === Master Animation Function (title + row) ===
            function animateTextFill(scope) {
                const isHighlightSection = $(scope).hasClass("highlight-section");
                const tl = gsap.timeline({ delay: 2 }); // delay after panel animation

                // Step 1: Animate "Energising a" (black fill)
                const blackChars = $(scope).find(".black .char");
                if (blackChars.length) {
                blackChars.each(function (i, char) {
                    tl.to(char, {
                    color: "#00000099",
                    duration: 0.10,
                    ease: "linear"
                    }, `+=0`);
                });
                }

                // Step 2: Animate "Sustainable Future" (green fill)
                const greenChars = $(scope).find(".green .char");
                if (greenChars.length) {

                    // if ($(scope).hasClass("main-banner")) {
                    //     // Step 2a: (Optional) set immediate base color to black if needed
                    //     tl.set(greenChars, { color: "#000000", duration: 0.02 });
                    // }

                    // Step 2b: Animate each char to green
                    greenChars.each(function (i, char) {
                        tl.to(char, {
                        color: "#00A99D",
                        duration: 0.10,
                        ease: "linear"
                        }, `+=0`);
                    });
                }

                if (isHighlightSection) {
                    // Fade in the text container after title animation
                    const textContainer = $(scope).find(".line-animation-text");
                    
                    // Check if this text container has already been animated
                    if (!textContainer.hasClass('animated-text')) {
                        textContainer.addClass('animated-text');
                        tl.fromTo(textContainer, 
                            { opacity: 0, y: 20 },
                            { opacity: 1, y: 0, duration: 0.5, ease: "power2.out" },
                            "+=0.3" // slight delay after title animation
                        );
                    }
                }

                // Step 3: Word-by-word subtitle animation
                $(scope).find(".word").each(function (i, el) {
                    tl.to(el, {
                        color: "#00000099",
                        duration: 0.3,
                        ease: "linear"
                    }, `+=0.02`);
                });

                // Step 4: Paragraph row-by-row animation (if exists)
                $(scope).find(".line-row").each(function (i, el) {
                    tl.to(el, {
                        color: "#00A99D",
                        duration: 0.5,
                        ease: "linear"
                    }, `+=0.005`);
                });

            }

            // overview 
            if (!isTablet) {
                $(".scrol-text-animation1 .section-content .text-animation-rows").each(function (_, element) {
                    const tl = gsap.timeline({
                        scrollTrigger: {
                            trigger: element,
                            start: "top center+=300",
                            end: "bottom center",
                            toggleActions: "play none none none",
                            scrub: true,
                        }
                    });

                    $(element).find(".line-row").each(function (i, row) {
                        tl.to(row, {
                            color: "#00A99D",
                            duration: 0.5,
                            ease: "linear"
                        }, `+=0.01`);
                    });
                });
            } else {
                const overviewBlockObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                    const rows = entry.target.querySelectorAll(".line-row");

                    rows.forEach((row, index) => {
                        setTimeout(() => {
                        row.classList.add("line-in-view");
                        }, index * 120); // Delay between lines (adjust as needed)
                    });

                    overviewBlockObserver.unobserve(entry.target);
                    }
                });
                }, {
                threshold: 0.4,
                rootMargin: "0px 0px -10% 0px"
                });

                // Observe each block instead of each line
                document.querySelectorAll(".scrol-text-animation1 .section-content .text-animation-rows")
                .forEach(block => overviewBlockObserver.observe(block));

            }
            if (!isTablet) {
              $(".financial-statement-section").each(function (_, element) {
                const tl = gsap.timeline({
                    scrollTrigger: {
                        trigger: element,
                        start: "top 30%",
                        end: "bottom center",
                        toggleActions: "play none none none",
                    }
                });

                // 1. First animate the background image
                tl.to(".financial-statement-background", {
                    opacity: 1,
                    scale: 1.01,
                    duration: 1,
                    ease: "power2.inOut"
                });

                // 1. First animate the text lines
                $(element).find(".line-row").each(function (i, row) {
                    tl.to(row, {
                        y: 0,
                        opacity: 1,
                        color: "#00A99D",
                        duration: 1,
                        ease: "power3.out"
                    }, i * 0.3); // Staggered start
                }, ">");
                
                // 2. Then animate the right section (book image)
                tl.to(".financial-statement-right", {
                    opacity: 1,
                    transform: "translateX(0%)",
                    duration: 1,
                    ease: "power2.out"
                }, "-=0.5"); // Start 0.5 seconds before the text animation completes
                
                // 3. Finally animate the CTA button
                tl.to(".financial-statement-section .read-more-cta", {
                    opacity: 1,
                    transform: "translateX(0)",
                    duration: 0.8,
                    ease: "power2.out"
                }, ">");

              });
            } else {
               const fsObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                        const el = entry.target;
                        el.classList.add("in-view");

                        // Animate text lines one by one
                        const rows = el.querySelectorAll(".line-row");
                        rows.forEach((row, i) => {
                            setTimeout(() => row.classList.add("line-in-view"), i * 300);
                        });

                        fsObserver.unobserve(el); // Remove if you want re-trigger
                        }
                    });
                }, { threshold: 0.4 });

                // Observe all sections
                document.querySelectorAll(".financial-statement-section").forEach(section => {
                    fsObserver.observe(section);
                });
            }

            if (!isTablet) {
                $("#financial").each(function (_, element) {
                    const tl = gsap.timeline({
                        scrollTrigger: {
                            trigger: element,
                            start: "top 30%",
                            end: "bottom center",
                            toggleActions: "play none none none",
                        }
                    });
                    
                    tl.to(".full-panel h2", {
                        opacity: 1,
                        transform: "translateX(0%)",
                        duration: 1,
                        ease: "power2.out"
                    }); 
                    
                    tl.to(".left-panel h2", {
                        opacity: 1,
                        transform: "translateX(0%)",
                        duration: 1,
                        ease: "power2.out"
                    }); 

                    tl.to(".financial-intro", {
                        opacity: 1,
                        transform: "translateX(0)",
                        duration: 1,
                        ease: "power2.out"
                    }, "-=0.8");

                    tl.to(".tab-buttons", {
                        opacity: 1,
                        transform: "translateY(0)",
                        duration: 1,
                        ease: "power2.out"
                    }, "-=0.8");

                    tl.to(".financial-bars", {
                        opacity: 1,
                        transform: "translateX(0)",
                        duration: 1,
                        ease: "power2.out"
                    }, "-=0.8");

                    tl.to(".right-panel p#description", {
                        opacity: 1,
                        transform: "translateX(0)",
                        duration: 1,
                        ease: "power2.out"
                    }, "-=0.8");

                });
            }else{
                const financialObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                        entry.target.classList.add("in-view");
                        financialObserver.unobserve(entry.target); // Remove if you want repeatable animations
                        }
                    });
                    }, {
                    threshold: 0.3,
                    rootMargin: "0px 0px -20% 0px"
                });

                // Observe the full #financial section
                const financialSection = document.getElementById("financial");
                if (financialSection) financialObserver.observe(financialSection);

            }

            // Direct initialization for financial animations
            $(document).ready(function() {
                // Simple function to animate count-up elements
                function animateCountUp() {
                    $('.count-up').each(function() {
                        const $this = $(this);
                        const targetValue = parseFloat($this.data('value') || 0);
                        
                        // Reset to zero
                        $this.text('0.0');
                        
                        // Animation variables
                        let currentValue = 0;
                        const duration = 1500;
                        const interval = 16;
                        const increment = targetValue / (duration / interval);
                        
                        // Clear any existing animation
                        if ($this.data('countTimer')) {
                            clearInterval($this.data('countTimer'));
                        }
                        
                        // Start new animation
                        const timer = setInterval(() => {
                            currentValue += increment;
                            
                            if (currentValue >= targetValue) {
                                $this.text(targetValue.toFixed(1));
                                clearInterval(timer);
                            } else {
                                $this.text(currentValue.toFixed(1));
                            }
                        }, interval);
                        
                        // Store timer reference
                        $this.data('countTimer', timer);
                    });
                }
                
                // Function to trigger bar animations
                function triggerBarAnimations() {
                    // First reset any existing animations
                    $(".financial-bar-2023, .financial-bar-2024").removeClass("animate");
                    
                    // Get the active tab data
                    const activeKey = $(".tabs-item.active").data("key");
                    const data = financialData[activeKey];
                    
                    if (!data) return;
                    
                    // Reset widths to 0 and temporarily hide bars
                    $(".financial-bar-2023, .financial-bar-2024").css({
                        "width": "0%",
                        "opacity": "0"
                    });
                    
                    // Force reflow to ensure animation works
                    void $(".financial-bar-2023")[0]?.offsetWidth;
                    
                    // Calculate responsive widths
                    const calculateResponsiveWidth = (baseWidth) => {
                        const screenWidth = window.innerWidth;
                        let width = parseFloat(baseWidth);
                        if (screenWidth < 480) return `${width * 0.9}%`;
                        else if (screenWidth < 768) return `${width * 0.95}%`;
                        else return `${width}%`;
                    };
                    
                    // Get width values from data
                    const width2023 = data.width2023 || "100";
                    const width2024 = data.width2024 || "100";
                    
                    // Apply responsive widths
                    const responsiveWidth2023 = calculateResponsiveWidth(width2023);
                    const responsiveWidth2024 = calculateResponsiveWidth(width2024);
                    
                    // After a short delay, show the bars and start animation
                    setTimeout(() => {
                        // Make bars visible again
                        $(".financial-bar-2023, .financial-bar-2024").css("opacity", "1");
                        
                        // Start animations with staggered timing
                        $(".financial-bar-2023").css("width", responsiveWidth2023).addClass("animate");
                        setTimeout(() => {
                            $(".financial-bar-2024").css("width", responsiveWidth2024).addClass("animate");
                        }, 250);
                        
                        // Also trigger SVG animations if needed
                        ['barAnimation2023', 'barAnimation2024'].forEach((id, index) => {
                            const barAnim = document.getElementById(id);
                            if (barAnim) {
                                const newBar = barAnim.cloneNode(true);
                                barAnim.parentNode.replaceChild(newBar, barAnim);
                                
                                if (index === 1) {
                                    setTimeout(() => newBar.beginElement(), 100);
                                } else {
                                    newBar.beginElement();
                                }
                            }
                        });
                    }, 50);
                }
                
                // Initialize animations
                triggerBarAnimations();
                animateCountUp();
                
                // Also trigger on scroll into view
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            triggerBarAnimations();
                            animateCountUp();
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.2 });
                
                const financialSection = document.getElementById('financial');
                if (financialSection) {
                    observer.observe(financialSection);
                }
                
                // Handle tab clicks
                $(".tabs-item").on("click", function() {
                    $(".tabs-item").removeClass("active");
                    $(this).addClass("active");
                    
                    // Get the active tab data
                    const activeKey = $(this).data("key");
                    const data = financialData[activeKey];
                    
                    if (data) {
                        // Immediately hide the bars before starting new animation
                        $(".financial-bar-2023, .financial-bar-2024").css({
                            "width": "0%",
                            "opacity": "0"
                        });
                        
                        // Update description and labels
                        $("#description").text(data.text);
                        $("#label2023").html(`RM <span class="count-up" data-value="${data.label2023.match(/\d+\.?\d*/)[0]}">0</span> <br>billion`);
                        $("#label2024").html(`RM <span class="count-up" data-value="${data.label2024.match(/\d+\.?\d*/)[0]}">0</span> <br>billion`);
                        
                        // Force reflow to ensure animation works
                        void $(".financial-bar-2023")[0]?.offsetWidth;
                        
                        // After a short delay, show the bars and start animation
                        setTimeout(() => {
                            // Make bars visible again
                            $(".financial-bar-2023, .financial-bar-2024").css("opacity", "1");
                            
                            // Trigger animations
                            triggerBarAnimations();
                            animateCountUp();
                        }, 50);
                    }
                });
                
                // Set initial scales on page load
                const activeKey = $(".tabs-item.active").data("key");
                const data = financialData[activeKey];
                
                if (data) {
                    // Set initial scale for 2023
                    $(".financial-bar-row:first-child .financial-bar svg g").css({
                        'transform': 'scaleX(1)',
                        'transform-origin': 'left center'
                    });
                    
                    // Set initial scale for 2024
                    $(".financial-bar-row:nth-child(2) .financial-bar svg g").css({
                        'transform': `scaleX(${data.scaleX2024 || 1})`,
                        'transform-origin': 'left center'
                    });
                    
                    // Ensure clip rectangles are reset
                    document.querySelectorAll('.financial-bar-svg').forEach(svg => {
                        svg.style.opacity = '1';
                    });
                    
                    // Trigger initial animations
                    setTimeout(() => {
                        triggerBarAnimations();
                    }, 100);
                }
            });

             if (!isTablet) {
                $(".key-message-content").each(function (_, element) {
                    const tl = gsap.timeline({
                        scrollTrigger: {
                            trigger: element,
                            start: "top 30%",
                            end: "bottom center",
                            toggleActions: "play none none none",
                        }
                    });
                    
                    // Get columns based on their semantic classes
                    const imageCol = $(element).find(".image-column");
                    const msgCol = $(element).find(".message-column");
                    
                    const isImageRight = $(element).hasClass("image-right");
                    
                    // Set initial states based on layout class
                    if (isImageRight) {
                        // Image is on right, message on left
                        gsap.set(imageCol, { 
                            xPercent: -50,
                            opacity: 0,
                            zIndex: 2
                        });
                        
                        gsap.set(msgCol, { 
                            xPercent: 50,
                            opacity: 0,
                            zIndex: 1
                        });
                    } else {
                        // Image is on left, message on right (default)
                        gsap.set(imageCol, { 
                            xPercent: 50,
                            opacity: 0,
                            zIndex: 2
                        });
                        
                        gsap.set(msgCol, { 
                            xPercent: -50,
                            opacity: 0,
                            zIndex: 1
                        });
                    }
                    
                    // Animation sequence remains the same
                    // Animation sequence with mobile check
                    if (window.innerWidth > 768) {
                        tl.to(imageCol, {
                            opacity: 1,
                            duration: 0.8,
                            ease: "power2.out"
                        })
                        .to(imageCol, {
                            xPercent: 0,
                            duration: 1,
                            ease: "power3.inOut"
                        }, "+=0.3")
                    } else {
                        tl.to(imageCol, {
                            opacity: 1,
                            xPercent: 0,
                            duration: 1,
                            ease: "power3.inOut"
                        });
                    }
                    tl.to(msgCol, {
                        xPercent: 0,
                        opacity: 1,
                        duration: 1,
                        ease: "power3.inOut"
                    }, "-=0.8")
                    .to($(element).find(".quote-content, .quote-message"), {
                        opacity: 1,
                        y: 0,
                        duration: 1,
                        // stagger: 0.15,
                        ease: "power3.out"
                    }, "-=0.3")
                    .to($(element).find(".quote-wrapper .bgelements"), {
                        opacity: 1,
                        duration: 2,
                        transform: "translateX(0)",
                        ease: "power3.out" // Fixed capitalization of "out"
                    }, "-=0.8")
                    .to($(element).find(".read-more-cta"), { // More specific to current element
                        opacity: 1,
                        duration: 0.8,
                        transform: "translateX(0)",
                        ease: "power2.out" // Fixed capitalization of "out"
                    }, "-=1.5");
                });
             } else {
                const kmObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                        const el = entry.target;
                        el.classList.add("in-view");

                        // Optionally remove for re-entrance animation
                        kmObserver.unobserve(el);
                        }
                    });
                    }, {
                    threshold: 0.4,
                    rootMargin: "0px 0px -20% 0px"
                    });

                    // Observe each .key-message-content block
                    document.querySelectorAll(".key-message-content").forEach(section => {
                    // Set initial direction classes based on layout
                    if (section.classList.contains("image-right")) {
                        section.classList.add("image-right-init");
                    } else {
                        section.classList.add("image-left-init");
                    }

                    kmObserver.observe(section);
                });

             }

             if (!isTablet) {
              $(".energy-transition-strategy").each(function (_, element) {
                    const tl = gsap.timeline({
                        scrollTrigger: {
                            trigger: element,
                            start: "top 30%",
                            end: "bottom center",
                            toggleActions: "play none none none",
                        }
                    });
                    
                    tl.to(".energy-transition-strategy h2", {
                        opacity: 1,
                        duration: 1,
                        ease: "power2.out"
                    }); 

                    tl.to(".ets-card", {
                        opacity: 1,
                        transform: "translateY(0)",
                        duration: 1,
                        ease: "power2.out"
                    }, "-=0.8");

                    tl.to(".ets-card-content", {
                        opacity: 1,
                        transform: "translateY(0)",
                        duration: 1,
                        ease: "power2.out"
                    }, "-=0.8");

                });
            } else {
                const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                    entry.target.classList.add("in-view");
                    observer.unobserve(entry.target);
                    }
                });
                }, {
                threshold: 0.4,
                rootMargin: "0px 0px -20% 0px"
                });

                document.querySelectorAll(".energy-transition-strategy")
                .forEach(section => observer.observe(section));

            }

            if (!isTablet) {
                 $("#non-financial-content-index").each(function (_, element) {
                    const tl = gsap.timeline({
                        scrollTrigger: {
                            trigger: element,
                            start: "top 30%",
                            end: "bottom center",
                            toggleActions: "play none none none",
                        }
                    });
                    tl.to(".accordion", {
                        opacity: 1,
                        transform: "translateY(0)",
                        duration: 1,
                        ease: "power2.out"
                    });
                });
            } else {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                        entry.target.classList.add("in-view");
                        observer.unobserve(entry.target); // remove this line if you want it to repeat on re-entry
                        }
                    });
                    }, {
                    threshold: 0.1,
                    rootMargin: "0px 0px -0% 0px" // Trigger slightly before center
                });

                // Apply to each .accordion inside #non-financial-content-index
                document.querySelectorAll("#non-financial-content-index .accordion")
                .forEach(acc => observer.observe(acc));
            }


            // cards stack
            if (!isTablet) {
            const cards = gsap.utils.toArray(".card");
            const section = document.querySelector("#energy-strategy-cards");

            let tlcards = gsap.timeline({
                scrollTrigger: {
                trigger: section,
                start: "top top",
                end: () => "+=" + cards.length * 585,
                scrub: true,
                pin: section,
                anticipatePin: 1,
                onLeave: () => {
                        // ScrollTrigger.refresh(true); // refresh after unpinning to fix downstream triggers
                    }
                }
            });

            cards.forEach((card, index) => {
                const scale = 1 - index * 0.20;
                const offsetY = index * - 150;

                // Animate card in
                const initialY = index === 0 ? "10%" : "200%";
                tlcards.fromTo(card, 
                    { opacity: 1, y: initialY }, 
                    { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" }
                );

                // Animate scale & position of current and previous cards
                cards.slice(0, index).forEach((prevCard, j) => {
                    tlcards.to(prevCard, {
                        scale: 1 - (index - j) * 0.20,
                        y: -(index - j) * 150,
                        opacity: 1,
                        duration: 0.3,
                        ease: "power2.out"
                    }, "<"); // sync
                    // Animate ::after via CSS variable
                    tlcards.to(prevCard, {
                        css: {
                        "--after-opacity": 1,
                        },
                        duration: 0.3
                    }, "<");

                    // Animate .card-content margin-top via CSS variable
                    const content = prevCard.querySelector(".card-content");
                    if (content) {
                        tlcards.to(content, {
                        y:-30,
                        duration: 0.3
                        }, "<");
                    }
                });
            }); 
        } else{
             const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                        entry.target.classList.add("in-view");
                        observer.unobserve(entry.target); // remove this line if you want it to repeat on re-entry
                        }
                    });
                    }, {
                    threshold: 0.5,
                    rootMargin: "0px 0px -20% 0px" // Trigger slightly before center
                });

                // Apply to each card inside 
                document.querySelectorAll("#energy-strategy-cards .card")
                .forEach(tlcard => observer.observe(tlcard));
        }

            if (!isTablet) {
                // === Scroll-triggered rows animation (text-animation-rows) ===
                $(".scrol-text-animation2 .section-content .text-animation-rows").each(function (_, element) {
                    const tl = gsap.timeline({
                        scrollTrigger: {
                            trigger: element,
                            start: "top center",
                            end: "bottom center",
                            toggleActions: "play none none none",
                            scrub: true,
                        }
                    });

                    $(element).find(".line-row").each(function (i, row) {
                        tl.to(row, {
                            color: "#00A99D",
                            duration: 0.2,
                            ease: "linear"
                        }, `+=0.01`);
                    });
                });
            } else {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                        entry.target.classList.add("in-view");
                        observer.unobserve(entry.target); // remove this line if you want it to repeat
                        }
                    });
                }, {
                threshold: 0.6,
                    rootMargin: "-30% 0px -30% 0px" // Trigger when the row is near center
                });

                // Target each individual .line-row
                document.querySelectorAll(".scrol-text-animation2 .section-content .text-animation-rows .line-row")
                .forEach(row => observer.observe(row));
            }
            
            // Financial section data and functionality
            const financialData = {
                revenue: {
                    label2023: "RM 343.6 billion",
                    label2024: "RM 320.0 billion",
                    scaleX2023: 0.82,
                    scaleX2024: 0.70,    // 320.0/343.6 ≈ 0.92
                    text: "Revenue declined by seven per cent from the previous year, primarily due to lower average realised LNG prices and divestment of the Engen Group, partially offset by higher sales volume."
                },
                pat: {
                    label2023: "RM 80.7 billion",
                    label2024: "RM 55.1 billion",
                    scaleX2023: 0.45,
                    scaleX2024: 0.3,    // 55.1/84.7 ≈ 0.65
                    text: "PAT reduced to RM55.1 billion in 2024, impacted by lower prices, absence of favourable tax adjustments from 2023, and foreign currency translation reserves from the Engen Group divestment."
                },
                totalassets: {
                    label2023: "RM 773.3 billion",
                    label2024: "RM 766.7 billion",
                    scaleX2023: 1,
                    scaleX2024: 0.95,    // 766.7/773.3 ≈ 0.99
                    text: "We maintained a strong asset base in 2024 with RM766.7 billion in total assets, reflecting continued financial resilience despite a marginal dip from 2023."
                },
                cffo: {
                    label2023: "RM 114.2 billion",
                    label2024: "RM 102.5 billion",
                    scaleX2023: 0.6,
                    scaleX2024: 0.55,    // 114.2/102.5 ≈ 1.11
                    text: "In 2024, we maintained a robust CFFO of RM102.5 billion, enabling us to sustain operations, explore growth opportunities, and fulfil our dividend commitments."
                }
            };

            // Financial section JavaScript
            const financialSection = {
                animateNumbers: function() {
                    // Clear any existing animations first
                    document.querySelectorAll('.count-up').forEach(element => {
                        if (element._countTimer) {
                            clearInterval(element._countTimer);
                            element._countTimer = null;
                        }
                    });

                    const activeKey = $(".tabs-item.active").data("key");
                    const data = financialData[activeKey];
                    
                    if (!data) return;

                    document.querySelectorAll('.count-up').forEach(element => {
                        const parent = element.closest('.financial-bar-label');
                        const parentId = parent.id;
                        const valueStr = parentId === 'label2023' ? data.label2023 : data.label2024;
                        const match = valueStr.match(/\d+\.?\d*/);
                        const targetValue = match ? parseFloat(match[0]) : 0;

                        element.textContent = "0.0";
                        let currentValue = 0;
                        const duration = 1500;
                        const interval = 16;
                        const increment = targetValue / (duration / interval);

                        element._countTimer = setInterval(() => {
                            currentValue += increment;
                            if (currentValue >= targetValue) {
                                element.textContent = targetValue.toFixed(1);
                                clearInterval(element._countTimer);
                                element._countTimer = null;
                            } else {
                                element.textContent = currentValue.toFixed(1);
                            }
                        }, interval);
                    });
                },

                updateBarScales: function() {
                    const activeKey = $(".tabs-item.active").data("key");
                    const data = financialData[activeKey];
                    
                    if (!data) return;
                    
                    // Reset SVG clip paths immediately
                    document.querySelectorAll('#clipRect2023, #clipRect2024').forEach(rect => {
                        if (rect) rect.setAttribute('width', '0');
                    });
                    
                    // Set the scale for both bars
                    const scaleX2023 = data.scaleX2023 || 1;
                    const scaleX2024 = data.scaleX2024 || 1;
                    
                    // Apply scales to SVGs - ensure this happens before animation
                    const svg2024 = document.querySelector('.financial-bar-row:nth-child(1) .financial-bar svg g');
                    const svg2023 = document.querySelector('.financial-bar-row:nth-child(2) .financial-bar svg g');
                    
                    if (svg2023) {
                        svg2023.style.transform = `scaleX(${scaleX2023})`;
                        svg2023.style.transformOrigin = 'left center';
                    }
                    
                    if (svg2024) {
                        svg2024.style.transform = `scaleX(${scaleX2024})`;
                        svg2024.style.transformOrigin = 'left center';
                    }
                    
                    // Ensure clip rectangles are reset
                    document.querySelectorAll('.financial-bar-svg').forEach(svg => {
                        svg.style.opacity = '1';
                    });
                    
                    // Trigger clip mask animations after a brief delay
                    setTimeout(() => {
                        this.animateBarClipMasks();
                    }, 50);
                },
                
                animateBarClipMasks: function() {
                    // First reset any existing animations
                    ['barAnimation2023', 'barAnimation2024'].forEach((id) => {
                        const barAnim = document.getElementById(id);
                        if (barAnim) {
                            // Reset the animation by cloning and replacing
                            const newBar = barAnim.cloneNode(true);
                            barAnim.parentNode.replaceChild(newBar, barAnim);
                        }
                    });
                    
                    // Make sure SVGs are visible before animation
                    document.querySelectorAll('.financial-bar svg').forEach(svg => {
                        svg.style.opacity = '1';
                    });
                    
                    // Trigger the animations with a slight delay
                    setTimeout(() => {
                        const barAnim2023 = document.getElementById('barAnimation2023');
                        if (barAnim2023) barAnim2023.beginElement();
                        
                        // Add a slight delay for the second bar
                        setTimeout(() => {
                            const barAnim2024 = document.getElementById('barAnimation2024');
                            if (barAnim2024) barAnim2024.beginElement();
                        }, 100);
                    }, 50);
                },

                resetLabels: function() {
                    $("#label2023, #label2024").each(function() {
                        $(this).html(`RM <span class="count-up">0</span> <br>billion`);
                    });
                },

                updateContent: function(key) {
                    const data = financialData[key];
                    if (!data) return;

                    // Update description text
                    $("#description").text(data.text);
                    
                    // Update labels
                    $("#label2023").html(`RM <span class="count-up" data-value="${data.label2023.match(/\d+\.?\d*/)[0]}">0</span> <br>billion`);
                    $("#label2024").html(`RM <span class="count-up" data-value="${data.label2024.match(/\d+\.?\d*/)[0]}">0</span> <br>billion`);
                },

                init: function() {
                    // Set default active tab if none is selected
                    if (!$(".tabs-item.active").length) {
                        $(".tabs-item").first().addClass("active");
                    }

                    const activeKey = $(".tabs-item.active").data("key");
                    const data = financialData[activeKey];
                    
                    // Immediately reset clip paths to ensure bars start hidden
                    document.querySelectorAll('#clipRect2023, #clipRect2024').forEach(rect => {
                        if (rect) rect.setAttribute('width', '0');
                    });
                    
                    // Set initial scales for both bars
                    if (data) {
                        const scaleX2023 = data.scaleX2023 || 1;
                        const scaleX2024 = data.scaleX2024 || 1;
                        
                        const svg2024 = document.querySelector('.financial-bar-row:nth-child(1) .financial-bar svg g');
                        const svg2023 = document.querySelector('.financial-bar-row:nth-child(2) .financial-bar svg g');
                        
                        if (svg2023) svg2023.style.transform = `scaleX(${scaleX2023})`;
                        if (svg2024) svg2024.style.transform = `scaleX(${scaleX2024})`;
                    }
                    
                    // Update content and animations
                    this.updateContent(activeKey);
                    this.updateBarScales();
                    this.animateNumbers();
                },

                updateTab: function(key) {
                    const data = financialData[key];
                    if (!data) return;
                    
                    // Immediately hide SVG content by resetting clip paths
                    document.querySelectorAll('#clipRect2023, #clipRect2024').forEach(rect => {
                        if (rect) rect.setAttribute('width', '0');
                    });
                    
                    // Reset scales for both bars
                    const svg2024 = document.querySelector('.financial-bar-row:nth-child(1) .financial-bar svg g');
                    const svg2023 = document.querySelector('.financial-bar-row:nth-child(2) .financial-bar svg g');
                
                    
                    // Update content
                    this.updateContent(key);
                    
                    // Reset labels
                    this.resetLabels();
                    
                    // Short delay before starting animations
                    setTimeout(() => {
                        // Start animations
                        this.animateBarClipMasks();
                        
                        // Animate numbers
                        this.animateNumbers();

                        if (svg2023) svg2023.style.transform = `scaleX(${data.scaleX2023 || 1})`;
                        if (svg2024) svg2024.style.transform = `scaleX(${data.scaleX2024 || 1})`;
                    }, 50);
                }
            };

            $(document).ready(function() {
                // First tab setup
                if (!$(".tabs-item.active").length) {
                    $(".tabs-item").first().addClass("active");
                }

                // Immediately reset clip paths to ensure bars start hidden
                document.querySelectorAll('#clipRect2023, #clipRect2024').forEach(rect => {
                    if (rect) rect.setAttribute('width', '0');
                });

                // Scroll trigger to initialize when section is visible
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            financialSection.init();
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.2 });

                const financialSectionElement = document.getElementById('financial');
                if (financialSectionElement) observer.observe(financialSectionElement);

                // Tab click event
                $(".tabs-item").on("click", function() {
                    $(".tabs-item").removeClass("active");
                    $(this).addClass("active");
                    const key = $(this).data("key");
                    financialSection.updateTab(key);
                });
            });



            
        });


        // downloads section
        $(document).ready(function () {
            const small_mobile_viewport = window.innerWidth <= 768;
            // CYCLIC LOOP OBSERVER \/ \/ \/
            let options = {
                root: null,
                threshold: small_mobile_viewport ? 0.8 : 0.5, // fully visible
            };
            

            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const group = entry.target;

                        group.querySelector('.center-circle')?.classList.add('animated');
                        group.querySelector('.green')?.classList.add('animated');
                        group.querySelector('.blue')?.classList.add('animated');
                        group.querySelector('.yellow-top')?.classList.add('animated');
                        group.querySelector('.yellow-bottom')?.classList.add('animated');
                        group.querySelectorAll('.cta').forEach(cta => {
                            cta.classList.add('animated');
                        });

                        observer.unobserve(group); // 🚫 Only trigger once
                    }
                });
            }, options);
            // Target the specific section
            const csv_section = document.getElementById('creating-sustainable-value');
            if (csv_section) {
                observer.observe(csv_section);
            }

            // CYCLIC LOOP OBSERVER /\ /\ /\
            const $carousel = $('#carousel-download');
            const $activeCard = $('#active-card');
            const $carouselTitle = $('#carousel-title');
            const $downloadButton = $('.download-button');
            const $wrapper = $('.carousel-wrapper');
            const isMobile = $(window).width() <= 768;
            
        //     // Handle download button click
            $downloadButton.on('click', function(event) {
                event.preventDefault();
                const url = $(this).attr('data-download-files');
                const filename = $(this).attr('data-download-filenames');
                
                // Check if the URL is an external link (starts with http or https)
                if (url.startsWith('http://') || url.startsWith('https://')) {
                    // For external links, just open in a new tab
                    window.open(url, '_blank');
                } else {
                    // For local files, try to use saveAs if available
                    if (typeof saveAs === 'function') {
                        saveAs(url, filename);
                        // console.log('downloaded:', filename);
                    } else {
                        // Direct download fallback
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = filename || '';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        // console.log('direct download:', filename);
                    }
                }
            });
        });




       </script>

</body>

</html>
